-- 检查所有可能包含向量字段的表
-- 连接数据库: psql -h localhost -U postgres -d postgres

\echo '=== 检查所有向量相关表的维度 ==='

-- 1. 检查所有包含vector类型的列
SELECT 
    t.table_name,
    c.column_name,
    pg_catalog.format_type(a.atttypid, a.atttypmod) as vector_type
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name
JOIN pg_attribute a ON a.attname = c.column_name
JOIN pg_class cl ON a.attrelid = cl.oid
JOIN pg_namespace n ON cl.relnamespace = n.oid
WHERE t.table_schema = 'cscsrag'
AND n.nspname = 'cscsrag'
AND cl.relname = t.table_name
AND pg_catalog.format_type(a.atttypid, a.atttypmod) LIKE '%vector%'
ORDER BY t.table_name, c.column_name;

\echo ''
\echo '=== 检查所有包含embedding字段的表 ==='

-- 2. 检查所有包含embedding字段的表
SELECT 
    t.table_name,
    c.column_name,
    c.data_type,
    CASE 
        WHEN c.data_type = 'USER-DEFINED' THEN 
            pg_catalog.format_type(a.atttypid, a.atttypmod)
        ELSE c.data_type 
    END as full_type
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name
LEFT JOIN pg_attribute a ON a.attname = c.column_name
LEFT JOIN pg_class cl ON a.attrelid = cl.oid
LEFT JOIN pg_namespace n ON cl.relnamespace = n.oid
WHERE t.table_schema = 'cscsrag'
AND (n.nspname = 'cscsrag' OR n.nspname IS NULL)
AND (cl.relname = t.table_name OR cl.relname IS NULL)
AND c.column_name LIKE '%embedding%'
ORDER BY t.table_name, c.column_name;

\echo ''
\echo '=== 检查chunks表的vec字段 ==='

-- 3. 专门检查chunks表的vec字段
SELECT 
    table_name,
    column_name,
    pg_catalog.format_type(a.atttypid, a.atttypmod) as vector_type
FROM information_schema.columns c
JOIN pg_attribute a ON a.attname = c.column_name
JOIN pg_class cl ON a.attrelid = cl.oid
JOIN pg_namespace n ON cl.relnamespace = n.oid
WHERE c.table_schema = 'cscsrag'
AND n.nspname = 'cscsrag'
AND cl.relname = c.table_name
AND c.column_name = 'vec'
AND c.table_name LIKE '%chunks%';

\echo ''
\echo '=== 所有可能需要删除的表（如果维度不匹配） ==='

-- 4. 列出所有可能需要处理的表
SELECT DISTINCT table_name 
FROM information_schema.columns 
WHERE table_schema = 'cscsrag' 
AND (column_name LIKE '%embedding%' OR column_name = 'vec')
ORDER BY table_name;
