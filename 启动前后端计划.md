# AnythingChat 项目启动前后端计划

## 项目概述

AnythingChat 是一个基于 R2R (RAG to Riches) 框架的知识库聊天应用，采用前后端分离架构。项目包含一个 Next.js 前端界面和一个基于 FastAPI 的 Python 后端服务。

## 项目技术栈分析

### 后端技术栈
- **核心框架**: R2R (RAG to Riches) - 基于 FastAPI 的 RAG 框架
- **Python 版本**: 3.10+ (推荐 3.12)
- **主要依赖**:
  - FastAPI >= 0.115.11
  - Uvicorn >= 0.27.0
  - PostgreSQL + pgvector (向量数据库)
  - Hatchet (工作流编排)
  - LiteLLM (多模型支持)
  - Unstructured (文档解析)
- **数据库**: PostgreSQL 15+ with pgvector extension
- **消息队列**: RabbitMQ (用于 Hatchet)
- **文档处理**: Unstructured service (端口 7275)
- **图聚类**: Graph clustering service (端口 7276)
- **主服务端口**: 7272

### 前端技术栈
- **框架**: Next.js 14.2.5
- **Node.js 版本**: 22+ (推荐 22 LTS)
- **包管理器**: pnpm (推荐)
- **主要依赖**:
  - React 18.3.1
  - TypeScript 5.5.2
  - Tailwind CSS 3.4.17
  - Radix UI 组件库
  - r2r-js SDK 0.4.34
- **开发端口**: 3005
- **生产端口**: 3000

### 外部服务依赖
- **LLM 提供商**: OpenAI, Anthropic, Azure, Google 等
- **向量数据库**: PostgreSQL with pgvector
- **文档解析**: Unstructured API
- **监控**: Sentry
- **认证**: 支持 OAuth (Google, GitHub)

## 环境准备步骤

### 1. Python 环境准备

#### 安装 Python 3.12
```bash
# Windows (使用 Chocolatey)
choco install python --version=3.12.0

# macOS (使用 Homebrew)
brew install python@3.12

# Ubuntu/Debian
sudo apt update
sudo apt install python3.12 python3.12-venv python3.12-pip
```

#### 安装 uv 包管理工具
```bash
# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# 或使用 pip 安装
pip install uv
```

### 2. Node.js 环境准备

#### 安装 Node.js 22 LTS
```bash
# 使用 nvm (推荐)
nvm install 22
nvm use 22

# 或直接下载安装
# 访问 https://nodejs.org/ 下载 Node.js 22 LTS
```

#### 安装 pnpm
```bash
npm install -g pnpm
# 或
corepack enable
corepack prepare pnpm@latest --activate
```

### 3. Docker 环境准备
```bash
# 安装 Docker Desktop
# Windows/macOS: 下载 Docker Desktop
# Linux: 
sudo apt update
sudo apt install docker.io docker-compose-plugin
sudo systemctl start docker
sudo systemctl enable docker
```

## 后端安装和启动步骤

### 1. 环境变量配置

创建环境变量文件：
```bash
cd backend
cp .env.example .env
```

编辑 `.env` 文件，配置必要的环境变量：
```bash
# LLM API Keys (至少配置一个)
export OPENAI_API_KEY=sk-your-openai-key
# export ANTHROPIC_API_KEY=your-anthropic-key

# 数据库配置
export R2R_POSTGRES_USER=postgres
export R2R_POSTGRES_PASSWORD=postgres
export R2R_POSTGRES_HOST=localhost
export R2R_POSTGRES_PORT=5432
export R2R_POSTGRES_DBNAME=r2r
export R2R_PROJECT_NAME=anythingchat

# R2R 配置
export R2R_CONFIG_NAME=full
export R2R_HOST=0.0.0.0
export R2R_PORT=7272
```

### 2. 使用 uv 安装后端依赖

```bash
cd backend/py

# 创建虚拟环境
uv venv --python 3.12

# 激活虚拟环境
# Windows
.venv\Scripts\activate
# macOS/Linux
source .venv/bin/activate

# 安装核心依赖
uv pip install -e ".[core]"

# 安装开发依赖 (可选)
uv pip install -e ".[dev]"
```

### 3. 启动后端服务

#### 方式一：使用 Docker (推荐)
```bash
cd backend

# 启动完整服务栈 (包括数据库、消息队列等)
r2r serve --docker --config-name=full

# 或使用 docker-compose
docker-compose -f compose.full.yaml up -d
```

#### 方式二：本地启动 (需要手动启动依赖服务)
```bash
# 确保 PostgreSQL 和 Redis 已启动
# 启动 R2R 服务
python -m r2r.serve --config-name=full --host=0.0.0.0 --port=7272
```

### 4. 验证后端服务
```bash
# 检查服务健康状态
curl http://localhost:7272/health

# 或使用 Python 客户端
python -c "from r2r import R2RClient; client = R2RClient('http://localhost:7272'); print(client.health())"
```

## 前端安装和启动步骤

### 1. 安装前端依赖
```bash
cd frontend

# 使用 pnpm 安装依赖
pnpm install --frozen-lockfile

# 或使用 npm
npm ci
```

### 2. 环境变量配置

创建 `.env.local` 文件：
```bash
# 后端 API 地址
NEXT_PUBLIC_R2R_DEPLOYMENT_URL=http://localhost:7272

# 默认登录凭据 (可选)
NEXT_PUBLIC_R2R_DEFAULT_EMAIL=<EMAIL>
NEXT_PUBLIC_R2R_DEFAULT_PASSWORD=change_me_immediately

# 禁用遥测 (可选)
R2R_DASHBOARD_DISABLE_TELEMETRY=true
```

### 3. 启动前端开发服务器
```bash
# 开发模式启动
pnpm dev
# 或
npm run dev

# 访问 http://localhost:3005
```

### 4. 构建生产版本
```bash
# 构建生产版本
pnpm build
# 或
npm run build

# 启动生产服务器
pnpm start
# 或
npm start
```

## 数据库和外部服务配置

### 1. PostgreSQL 配置

#### 使用 Docker 启动 PostgreSQL
```bash
docker run -d \
  --name postgres-r2r \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=r2r \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  ankane/pgvector
```

#### 手动安装 PostgreSQL + pgvector
```bash
# Ubuntu/Debian
sudo apt install postgresql postgresql-contrib
sudo apt install postgresql-15-pgvector

# 创建数据库和用户
sudo -u postgres psql
CREATE DATABASE r2r;
CREATE USER r2r_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE r2r TO r2r_user;
CREATE EXTENSION vector;
```

### 2. Hatchet 工作流服务

Hatchet 用于管理异步任务和工作流，通过 Docker Compose 自动启动：
```bash
# 检查 Hatchet 服务状态
docker-compose -f compose.full.yaml ps hatchet-engine
```

### 3. 文档处理服务

#### Unstructured 服务 (端口 7275)
用于解析各种文档格式，自动通过 Docker 启动。

#### Graph Clustering 服务 (端口 7276)
用于知识图谱的社区检测，自动通过 Docker 启动。

## 环境变量配置指南

### 后端环境变量

#### 必需配置
```bash
# LLM API Keys (至少配置一个)
OPENAI_API_KEY=sk-your-key
ANTHROPIC_API_KEY=your-key

# 数据库连接
R2R_POSTGRES_USER=postgres
R2R_POSTGRES_PASSWORD=postgres
R2R_POSTGRES_HOST=localhost
R2R_POSTGRES_PORT=5432
R2R_POSTGRES_DBNAME=r2r
```

#### 可选配置
```bash
# 其他 LLM 提供商
AZURE_API_KEY=your-azure-key
GOOGLE_API_KEY=your-google-key
GROQ_API_KEY=your-groq-key

# 文档解析服务
UNSTRUCTURED_API_KEY=your-unstructured-key
UNSTRUCTURED_API_URL=https://api.unstructured.io/general/v0/general

# 网络搜索
TAVILY_API_KEY=your-tavily-key
SERPER_API_KEY=your-serper-key

# 监控和错误追踪
R2R_SENTRY_DSN=your-sentry-dsn
```

### 前端环境变量
```bash
# 后端 API 地址
NEXT_PUBLIC_R2R_DEPLOYMENT_URL=http://localhost:7272

# 默认认证信息
NEXT_PUBLIC_R2R_DEFAULT_EMAIL=<EMAIL>
NEXT_PUBLIC_R2R_DEFAULT_PASSWORD=change_me_immediately

# Sentry 监控
NEXT_PUBLIC_SENTRY_DSN=your-sentry-dsn

# 其他可选配置
R2R_DASHBOARD_DISABLE_TELEMETRY=true
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-key
```

## 常见问题和解决方案

### 1. 后端启动问题

#### 问题：uv 安装依赖失败
```bash
# 解决方案：更新 uv 到最新版本
pip install --upgrade uv

# 或使用 pip 作为备选方案
pip install -e ".[core]"
```

#### 问题：PostgreSQL 连接失败
```bash
# 检查 PostgreSQL 服务状态
docker ps | grep postgres

# 重启 PostgreSQL 容器
docker restart postgres-r2r

# 检查连接配置
psql -h localhost -U postgres -d r2r -c "SELECT version();"
```

#### 问题：端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :7272

# 修改端口配置
export R2R_PORT=7273
```

### 2. 前端启动问题

#### 问题：pnpm 安装依赖失败
```bash
# 清理缓存重新安装
pnpm store prune
rm -rf node_modules pnpm-lock.yaml
pnpm install

# 或使用 npm 作为备选
npm ci
```

#### 问题：Next.js 构建失败
```bash
# 检查 Node.js 版本
node --version  # 应该是 22+

# 清理 Next.js 缓存
rm -rf .next
pnpm build
```

#### 问题：API 连接失败
```bash
# 检查后端服务状态
curl http://localhost:7272/health

# 检查环境变量配置
echo $NEXT_PUBLIC_R2R_DEPLOYMENT_URL
```

### 3. Docker 相关问题

#### 问题：Docker 容器启动失败
```bash
# 查看容器日志
docker-compose -f compose.full.yaml logs

# 重新构建镜像
docker-compose -f compose.full.yaml build --no-cache

# 清理 Docker 资源
docker system prune -a
```

#### 问题：数据库初始化失败
```bash
# 重置数据库
docker-compose -f compose.full.yaml down -v
docker-compose -f compose.full.yaml up -d

# 手动运行数据库迁移
r2r db upgrade
```

### 4. 性能优化问题

#### 问题：响应速度慢
```bash
# 检查系统资源使用
docker stats

# 调整并发限制
export R2R_CONCURRENT_REQUEST_LIMIT=64

# 优化数据库连接池
export R2R_POSTGRES_MAX_CONNECTIONS=512
```

## 验证服务正常运行的方法

### 1. 后端服务验证

#### 基础健康检查
```bash
# HTTP 健康检查
curl -X GET http://localhost:7272/health
# 预期响应: {"status": "ok"}

# 使用 Python 客户端
python -c "
from r2r import R2RClient
client = R2RClient('http://localhost:7272')
print('Health:', client.health())
print('Server info:', client.server_stats())
"
```

#### 功能测试
```bash
# 测试文档上传和检索
python -c "
from r2r import R2RClient
import tempfile
import os

client = R2RClient('http://localhost:7272')

# 创建测试文档
with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
    f.write('这是一个测试文档，用于验证 R2R 系统功能。')
    test_file = f.name

try:
    # 上传文档
    result = client.documents.create(file_path=test_file)
    print('文档上传成功:', result)

    # 搜索测试
    search_result = client.retrieval.search(query='测试文档')
    print('搜索结果:', len(search_result.results), '条')

    # RAG 测试
    rag_result = client.retrieval.rag(query='这个文档的内容是什么？')
    print('RAG 响应:', rag_result.results.generated_answer[:100])

finally:
    os.unlink(test_file)
"
```

### 2. 前端服务验证

#### 访问测试
```bash
# 检查前端服务状态
curl -I http://localhost:3005
# 预期响应: HTTP/1.1 200 OK

# 检查关键页面
curl -s http://localhost:3005 | grep -i "anythingchat"
```

#### 功能测试
1. 打开浏览器访问 `http://localhost:3005`
2. 检查页面是否正常加载
3. 测试登录功能（使用默认凭据）
4. 测试文档上传功能
5. 测试聊天对话功能
6. 检查控制台是否有错误信息

### 3. 集成测试

#### 端到端测试流程
```bash
# 1. 启动所有服务
docker-compose -f backend/compose.full.yaml up -d
cd frontend && pnpm dev &

# 2. 等待服务启动
sleep 30

# 3. 运行集成测试
python -c "
import requests
import time

# 检查后端
backend_health = requests.get('http://localhost:7272/health')
print('后端状态:', backend_health.json())

# 检查前端
frontend_health = requests.get('http://localhost:3005')
print('前端状态码:', frontend_health.status_code)

# 检查服务间通信
# 这里可以添加更多的集成测试逻辑
print('集成测试完成')
"
```

### 4. 监控和日志

#### 查看服务日志
```bash
# 后端日志
docker-compose -f backend/compose.full.yaml logs -f r2r

# 前端日志
cd frontend && pnpm dev  # 查看控制台输出

# 数据库日志
docker-compose -f backend/compose.full.yaml logs postgres
```

#### 性能监控
```bash
# 检查资源使用情况
docker stats

# 检查数据库连接
docker exec -it postgres-r2r psql -U postgres -d r2r -c "
SELECT count(*) as active_connections
FROM pg_stat_activity
WHERE state = 'active';
"
```

## 生产环境部署建议

### 1. 安全配置
- 修改默认管理员密码
- 配置 HTTPS 证书
- 设置防火墙规则
- 启用认证和授权

### 2. 性能优化
- 配置负载均衡
- 启用 Redis 缓存
- 优化数据库连接池
- 配置 CDN

### 3. 监控和日志
- 配置 Sentry 错误监控
- 设置日志聚合
- 配置性能监控
- 设置告警规则

### 4. 备份和恢复
- 定期备份数据库
- 配置文件备份
- 测试恢复流程
- 制定灾难恢复计划

---

## 总结

本文档提供了 AnythingChat 项目的完整启动指南，包括：

1. **技术栈分析**: 详细分析了前后端技术栈和依赖关系
2. **环境准备**: 提供了 Python、Node.js 和 Docker 环境的安装指南
3. **安装步骤**: 详细说明了使用 uv 工具安装后端依赖和前端依赖的步骤
4. **配置指南**: 提供了完整的环境变量配置说明
5. **问题解决**: 列出了常见问题和解决方案
6. **验证方法**: 提供了全面的服务验证和测试方法

按照本指南操作，您应该能够成功启动 AnythingChat 项目的前后端服务。如果遇到问题，请参考常见问题部分或查看相关日志进行排查。
