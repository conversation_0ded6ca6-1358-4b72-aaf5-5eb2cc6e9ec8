-- Reset graph tables to fix dimension mismatch
-- This script will drop existing graph tables so they can be recreated with 1024 dimensions

-- Connect to the database and run these commands
-- psql -h localhost -U postgres -d postgres

-- Drop graph tables in correct order (considering foreign key constraints)
DROP TABLE IF EXISTS cscsrag.graphs_communities CASCADE;
DROP TABLE IF EXISTS cscsrag.graphs_document_relations CASCADE;
DROP TABLE IF EXISTS cscsrag.graphs_document_entities CASCADE;
DROP TABLE IF EXISTS cscsrag.graphs_collection_relations CASCADE;
DROP TABLE IF EXISTS cscsrag.graphs_collection_entities CASCADE;
DROP TABLE IF EXISTS cscsrag.graphs_relations CASCADE;
DROP TABLE IF EXISTS cscsrag.graphs_entities CASCADE;
DROP TABLE IF EXISTS cscsrag.graphs CASCADE;

-- Optional: Also drop any related indexes
DROP INDEX IF EXISTS cscsrag.graph_collection_id_idx;

-- The system will automatically recreate these tables with the correct 1024 dimensions
-- when the R2R server starts up next time

SELECT 'Graph tables reset completed. Restart R2R server to recreate with 1024 dimensions.' as status;
