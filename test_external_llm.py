#!/usr/bin/env python3
"""
测试外部LLM配置的脚本
"""

import os
import sys
import asyncio
from openai import OpenAI, AsyncOpenAI

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-onCliIrxDPaFG2tXZ3zhpXrPu5MrqZeWujnswtjeN7AsiKz8"
os.environ["OPENAI_BASE_URL"] = "http://172.17.9.46:3000/v1"

def test_sync_client():
    """测试同步OpenAI客户端"""
    print("=== 测试同步OpenAI客户端 ===")
    try:
        client = OpenAI()
        print(f"客户端base_url: {client.base_url}")
        
        response = client.chat.completions.create(
            model="volcengine/deepseek-v3",
            messages=[
                {"role": "user", "content": "你好，请简单介绍一下你自己。"}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        print("响应成功!")
        print(f"模型: {response.model}")
        print(f"内容: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"同步客户端测试失败: {e}")
        return False

async def test_async_client():
    """测试异步OpenAI客户端"""
    print("\n=== 测试异步OpenAI客户端 ===")
    try:
        client = AsyncOpenAI()
        print(f"客户端base_url: {client.base_url}")
        
        response = await client.chat.completions.create(
            model="volcengine/deepseek-v3",
            messages=[
                {"role": "user", "content": "请用一句话介绍深度学习。"}
            ],
            max_tokens=100,
            temperature=0.1
        )
        
        print("响应成功!")
        print(f"模型: {response.model}")
        print(f"内容: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"异步客户端测试失败: {e}")
        return False

def test_r2r_config():
    """测试R2R配置"""
    print("\n=== 测试R2R配置 ===")
    try:
        # 添加R2R路径到sys.path
        sys.path.insert(0, 'backend/py')
        
        from core.base.abstractions import GenerationConfig
        from core.providers.llm.openai import OpenAICompletionProvider
        from core.base.providers.llm import CompletionConfig
        
        # 创建配置
        config = CompletionConfig(provider="openai")
        provider = OpenAICompletionProvider(config)
        
        # 创建生成配置
        gen_config = GenerationConfig(
            model="openai/volcengine/deepseek-v3",
            temperature=0.1,
            max_tokens_to_sample=100
        )
        
        # 测试同步调用
        messages = [{"role": "user", "content": "测试R2R配置，请回复'配置成功'"}]
        
        task = {
            "messages": messages,
            "generation_config": gen_config,
            "kwargs": {}
        }
        
        response = provider._execute_task_sync(task)
        print("R2R配置测试成功!")
        print(f"响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"R2R配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("开始测试外部LLM配置...")
    print(f"API Key: {os.environ.get('OPENAI_API_KEY', 'Not Set')}")
    print(f"Base URL: {os.environ.get('OPENAI_BASE_URL', 'Not Set')}")
    
    results = []
    
    # 测试同步客户端
    results.append(test_sync_client())
    
    # 测试异步客户端
    results.append(await test_async_client())
    
    # 测试R2R配置
    results.append(test_r2r_config())
    
    # 总结结果
    print("\n=== 测试结果总结 ===")
    test_names = ["同步OpenAI客户端", "异步OpenAI客户端", "R2R配置"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(results)
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(main())
