README.md
pyproject.toml
core/__init__.py
core/agent/__init__.py
core/agent/base.py
core/agent/rag.py
core/agent/research.py
core/base/__init__.py
core/base/abstractions/__init__.py
core/base/agent/__init__.py
core/base/agent/agent.py
core/base/agent/tools/registry.py
core/base/agent/tools/built_in/get_file_content.py
core/base/agent/tools/built_in/search_file_descriptions.py
core/base/agent/tools/built_in/search_file_knowledge.py
core/base/agent/tools/built_in/tavily_extract.py
core/base/agent/tools/built_in/tavily_search.py
core/base/agent/tools/built_in/web_scrape.py
core/base/agent/tools/built_in/web_search.py
core/base/api/models/__init__.py
core/base/parsers/__init__.py
core/base/parsers/base_parser.py
core/base/providers/__init__.py
core/base/providers/auth.py
core/base/providers/base.py
core/base/providers/crypto.py
core/base/providers/database.py
core/base/providers/email.py
core/base/providers/embedding.py
core/base/providers/file.py
core/base/providers/ingestion.py
core/base/providers/llm.py
core/base/providers/ocr.py
core/base/providers/orchestration.py
core/base/providers/scheduler.py
core/base/utils/__init__.py
core/configs/full.toml
core/configs/full_azure.toml
core/configs/full_lm_studio.toml
core/configs/full_ollama.toml
core/configs/gemini.toml
core/configs/lm_studio.toml
core/configs/ollama.toml
core/configs/r2r_azure.toml
core/configs/r2r_azure_with_test_limits.toml
core/configs/r2r_with_auth.toml
core/configs/tavily.toml
core/examples/__init__.py
core/examples/hello_r2r.py
core/examples/supported_file_types/py.py
core/main/__init__.py
core/main/abstractions.py
core/main/app.py
core/main/app_entry.py
core/main/config.py
core/main/api/v3/base_router.py
core/main/api/v3/chunks_router.py
core/main/api/v3/collections_router.py
core/main/api/v3/conversations_router.py
core/main/api/v3/documents_router.py
core/main/api/v3/graph_router.py
core/main/api/v3/indices_router.py
core/main/api/v3/prompts_router.py
core/main/api/v3/retrieval_router.py
core/main/api/v3/system_router.py
core/main/api/v3/users_router.py
core/main/assembly/__init__.py
core/main/assembly/builder.py
core/main/assembly/factory.py
core/main/assembly/utils.py
core/main/middleware/__init__.py
core/main/middleware/project_schema.py
core/main/orchestration/__init__.py
core/main/orchestration/hatchet/__init__.py
core/main/orchestration/hatchet/graph_workflow.py
core/main/orchestration/hatchet/ingestion_workflow.py
core/main/orchestration/simple/__init__.py
core/main/orchestration/simple/graph_workflow.py
core/main/orchestration/simple/ingestion_workflow.py
core/main/services/__init__.py
core/main/services/auth_service.py
core/main/services/base.py
core/main/services/graph_service.py
core/main/services/ingestion_service.py
core/main/services/maintenance_service.py
core/main/services/management_service.py
core/main/services/retrieval_service.py
core/parsers/__init__.py
core/parsers/media/__init__.py
core/parsers/media/audio_parser.py
core/parsers/media/bmp_parser.py
core/parsers/media/doc_parser.py
core/parsers/media/docx_parser.py
core/parsers/media/img_parser.py
core/parsers/media/odt_parser.py
core/parsers/media/pdf_parser.py
core/parsers/media/ppt_parser.py
core/parsers/media/pptx_parser.py
core/parsers/media/rtf_parser.py
core/parsers/structured/__init__.py
core/parsers/structured/csv_parser.py
core/parsers/structured/eml_parser.py
core/parsers/structured/epub_parser.py
core/parsers/structured/json_parser.py
core/parsers/structured/msg_parser.py
core/parsers/structured/org_parser.py
core/parsers/structured/p7s_parser.py
core/parsers/structured/rst_parser.py
core/parsers/structured/tsv_parser.py
core/parsers/structured/xls_parser.py
core/parsers/structured/xlsx_parser.py
core/parsers/text/__init__.py
core/parsers/text/css_parser.py
core/parsers/text/html_parser.py
core/parsers/text/js_parser.py
core/parsers/text/md_parser.py
core/parsers/text/python_parser.py
core/parsers/text/text_parser.py
core/parsers/text/ts_parser.py
core/providers/__init__.py
core/providers/auth/__init__.py
core/providers/auth/clerk.py
core/providers/auth/jwt.py
core/providers/auth/r2r_auth.py
core/providers/auth/supabase.py
core/providers/crypto/__init__.py
core/providers/crypto/bcrypt.py
core/providers/crypto/nacl.py
core/providers/database/__init__.py
core/providers/database/base.py
core/providers/database/chunks.py
core/providers/database/collections.py
core/providers/database/conversations.py
core/providers/database/documents.py
core/providers/database/filters.py
core/providers/database/graphs.py
core/providers/database/limits.py
core/providers/database/maintenance.py
core/providers/database/postgres.py
core/providers/database/prompts_handler.py
core/providers/database/tokens.py
core/providers/database/users.py
core/providers/database/utils.py
core/providers/database/prompts/__init__.py
core/providers/database/prompts/chunk_enrichment.yaml
core/providers/database/prompts/collection_summary.yaml
core/providers/database/prompts/dynamic_rag_agent.yaml
core/providers/database/prompts/dynamic_rag_agent_xml_tooling.yaml
core/providers/database/prompts/graph_communities.yaml
core/providers/database/prompts/graph_entity_description.yaml
core/providers/database/prompts/graph_extraction.yaml
core/providers/database/prompts/hyde.yaml
core/providers/database/prompts/rag.yaml
core/providers/database/prompts/rag_fusion.yaml
core/providers/database/prompts/static_rag_agent.yaml
core/providers/database/prompts/static_research_agent.yaml
core/providers/database/prompts/summary.yaml
core/providers/database/prompts/system.yaml
core/providers/database/prompts/vision_img.yaml
core/providers/database/prompts/vision_pdf.yaml
core/providers/email/__init__.py
core/providers/email/console_mock.py
core/providers/email/mailersend.py
core/providers/email/sendgrid.py
core/providers/email/smtp.py
core/providers/embeddings/__init__.py
core/providers/embeddings/litellm.py
core/providers/embeddings/ollama.py
core/providers/embeddings/openai.py
core/providers/embeddings/utils.py
core/providers/file/__init__.py
core/providers/file/postgres.py
core/providers/file/s3.py
core/providers/ingestion/__init__.py
core/providers/ingestion/r2r/base.py
core/providers/ingestion/unstructured/base.py
core/providers/llm/__init__.py
core/providers/llm/anthropic.py
core/providers/llm/azure_foundry.py
core/providers/llm/litellm.py
core/providers/llm/openai.py
core/providers/llm/r2r_llm.py
core/providers/llm/utils.py
core/providers/ocr/__init__.py
core/providers/ocr/mistral.py
core/providers/orchestration/__init__.py
core/providers/orchestration/hatchet.py
core/providers/orchestration/simple.py
core/providers/scheduler/__init__.py
core/providers/scheduler/apscheduler.py
core/utils/__init__.py
core/utils/context.py
core/utils/logging_config.py
core/utils/sentry.py
core/utils/serper.py
r2r/__init__.py
r2r/mcp.py
r2r/r2r.toml
r2r/serve.py
r2r.egg-info/PKG-INFO
r2r.egg-info/SOURCES.txt
r2r.egg-info/dependency_links.txt
r2r.egg-info/entry_points.txt
r2r.egg-info/requires.txt
r2r.egg-info/top_level.txt
sdk/__init__.py
sdk/async_client.py
sdk/models.py
sdk/sync_client.py
sdk/asnyc_methods/__init__.py
sdk/asnyc_methods/chunks.py
sdk/asnyc_methods/collections.py
sdk/asnyc_methods/conversations.py
sdk/asnyc_methods/documents.py
sdk/asnyc_methods/graphs.py
sdk/asnyc_methods/indices.py
sdk/asnyc_methods/prompts.py
sdk/asnyc_methods/retrieval.py
sdk/asnyc_methods/system.py
sdk/asnyc_methods/users.py
sdk/base/__init_.py
sdk/base/base_client.py
sdk/sync_methods/__init__.py
sdk/sync_methods/chunks.py
sdk/sync_methods/collections.py
sdk/sync_methods/conversations.py
sdk/sync_methods/documents.py
sdk/sync_methods/graphs.py
sdk/sync_methods/indices.py
sdk/sync_methods/prompts.py
sdk/sync_methods/retrieval.py
sdk/sync_methods/system.py
sdk/sync_methods/users.py
shared/__init__.py
shared/abstractions/__init__.py
shared/abstractions/base.py
shared/abstractions/document.py
shared/abstractions/exception.py
shared/abstractions/graph.py
shared/abstractions/llm.py
shared/abstractions/prompt.py
shared/abstractions/search.py
shared/abstractions/tool.py
shared/abstractions/user.py
shared/abstractions/vector.py
shared/api/models/__init__.py
shared/api/models/base.py
shared/api/models/auth/__init__.py
shared/api/models/auth/responses.py
shared/api/models/graph/__init__.py
shared/api/models/graph/responses.py
shared/api/models/ingestion/__init__.py
shared/api/models/ingestion/responses.py
shared/api/models/management/__init__.py
shared/api/models/management/responses.py
shared/api/models/retrieval/__init__.py
shared/api/models/retrieval/responses.py
shared/utils/__init__.py
shared/utils/base_utils.py
shared/utils/splitter/__init__.py
shared/utils/splitter/text.py