#%%
import os

from r2r import R2RClient

# Create an account at SciPhi Cloud https://app.sciphi.ai and set an R2R_API_KEY environment variable
# or set the base URL to your instance. E.g. R2RClient("http://localhost:7272")
os.environ["R2R_API_KEY"] = "your-api-key"

# Create a client
client = R2RClient()
#%%
import os
import tempfile

import requests

# Download the content from GitHub
url = "https://raw.githubusercontent.com/SciPhi-AI/R2R/refs/heads/main/py/core/examples/data/aristotle.txt"
response = requests.get(url)

# Create a temporary file to store the content
with tempfile.NamedTemporaryFile(
    delete=False, mode="w", suffix=".txt"
) as temp_file:
    temp_file.write(response.text)
    temp_path = temp_file.name

# Ingest the file
ingestion_response = client.documents.create(file_path=temp_path)
print(ingestion_response)

# Clean up the temporary file
os.unlink(temp_path)
#%%
print("Performing RAG...")
rag_response = client.retrieval.rag(
    query="What is the nature of the soul?",
)

print(rag_response["results"]["completion"])