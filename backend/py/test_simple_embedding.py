#!/usr/bin/env python3
"""
简单的embedding测试脚本
"""
import os

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["OPENAI_BASE_URL"] = "http://172.17.9.46:3000/v1"

try:
    import litellm
    print("✅ 成功导入litellm")
    
    # 测试1: 使用openai/前缀的模型名称
    print("\n🔄 测试1: 使用openai/aliyun/text-embedding-v4...")
    try:
        response = litellm.embedding(
            model="openai/aliyun/text-embedding-v4",
            input=["测试文本"],
            api_key="sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI",
            api_base="http://172.17.9.46:3000/v1"
        )
        print(f"✅ 测试1成功!")
        print(f"   - 向量维度: {len(response.data[0]['embedding'])}")
        print(f"   - 前3个值: {response.data[0]['embedding'][:3]}")
    except Exception as e:
        print(f"❌ 测试1失败: {str(e)}")
    
    # 测试2: 直接使用aliyun/text-embedding-v4
    print("\n🔄 测试2: 使用aliyun/text-embedding-v4...")
    try:
        response = litellm.embedding(
            model="aliyun/text-embedding-v4",
            input=["测试文本"],
            api_key="sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI",
            api_base="http://172.17.9.46:3000/v1"
        )
        print(f"✅ 测试2成功!")
        print(f"   - 向量维度: {len(response.data[0]['embedding'])}")
        print(f"   - 前3个值: {response.data[0]['embedding'][:3]}")
    except Exception as e:
        print(f"❌ 测试2失败: {str(e)}")
    
    # 测试3: 使用text-embedding-v4（去掉aliyun前缀）
    print("\n🔄 测试3: 使用text-embedding-v4...")
    try:
        response = litellm.embedding(
            model="text-embedding-v4",
            input=["测试文本"],
            api_key="sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI",
            api_base="http://172.17.9.46:3000/v1"
        )
        print(f"✅ 测试3成功!")
        print(f"   - 向量维度: {len(response.data[0]['embedding'])}")
        print(f"   - 前3个值: {response.data[0]['embedding'][:3]}")
    except Exception as e:
        print(f"❌ 测试3失败: {str(e)}")

except ImportError as e:
    print(f"❌ 导入失败: {str(e)}")
except Exception as e:
    print(f"❌ 其他错误: {str(e)}")
