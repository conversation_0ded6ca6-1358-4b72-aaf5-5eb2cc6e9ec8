2025-07-31 16:26:34 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-31 16:26:34 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://172.17.9.46:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-31 16:26:38 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-31 16:26:38 - [32mINFO[0m - API Base: http://172.17.9.46:3000/v1[0m
2025-07-31 16:26:38 - [32mINFO[0m - Initializing EmbeddingProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={'api_base': 'http://172.17.9.46:3000/v1'} provider='newapi' base_model='aliyun/text-embedding-v4' base_dimension=1024 rerank_model=None rerank_url=None batch_size=128 concurrent_request_limit=256 max_retries=3 initial_backoff=1 max_backoff=64.0 quantization_settings=VectorQuantizationSettings(quantization_type=<VectorQuantizationType.FP32: 'FP32'>).[0m
2025-07-31 16:26:42 - [32mINFO[0m - Initialized NewAPIEmbeddingProvider with model: aliyun/text-embedding-v4[0m
2025-07-31 16:26:42 - [32mINFO[0m - API Base: http://172.17.9.46:3000/v1[0m
2025-07-31 16:26:42 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 16:26:42 - [32mINFO[0m - Initializing R2RCompletionProvider...[0m
2025-07-31 16:26:42 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 16:26:49 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 16:26:51 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 16:26:51 - [32mINFO[0m - Initializing CompletionProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' generation_config=GenerationConfig(model=None, temperature=0.1, top_p=1.0, max_tokens_to_sample=4096, stream=False, functions=None, tools=None, add_generation_kwargs={}, api_base=None, response_format=None, extended_thinking=False, thinking_budget=None, reasoning_effort=None) concurrent_request_limit=64 max_retries=3 initial_backoff=1.0 max_backoff=64.0 request_timeout=60.0[0m
2025-07-31 16:26:51 - [32mINFO[0m - Initializing BcryptCryptoProvider[0m
2025-07-31 16:26:51 - [32mINFO[0m - Initializing DatabaseProvider with config app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='postgres' user=None password=None host=None port=None db_name=None project_name=None postgres_configuration_settings=None default_collection_name='Default' default_collection_description='Your default collection.' collection_summary_system_prompt='system' collection_summary_prompt='collection_summary' disable_create_extension=False batch_size=1 graph_search_results_store_path=None graph_enrichment_settings=GraphEnrichmentSettings(force_graph_search_results_enrichment=False, graph_communities_prompt='graph_communities', max_summary_input_length=65536, generation_config=None, leiden_params={}) graph_creation_settings=GraphCreationSettings(graph_extraction_prompt='graph_extraction', graph_entity_description_prompt='graph_entity_description', entity_types=[], relation_types=[], chunk_merge_count=2, max_knowledge_relationships=100, max_description_input_length=65536, generation_config=None, automatic_deduplication=True) graph_search_settings=GraphSearchSettings(limits={}, enabled=True) limits=LimitSettings(global_per_min=60, route_per_min=20, monthly_limit=10000) maintenance=MaintenanceSettings(vacuum_schedule='0 3 * * *', vacuum_analyze=True, vacuum_full=False) route_limits={} user_limits={}.[0m
2025-07-31 16:26:51 - [32mINFO[0m - Connecting to Postgres via TCP/IP[0m
2025-07-31 16:26:51 - [32mINFO[0m - Initializing `PostgresDatabaseProvider`.[0m
2025-07-31 16:26:51 - [32mINFO[0m - Connecting with 230 connections to `asyncpg.create_pool`.[0m
2025-07-31 16:26:52 - [32mINFO[0m - Successfully connected to Postgres database and created connection pool.[0m
2025-07-31 16:26:52 - [32mINFO[0m - Creating table, if it does not exist: "cscsrag"."documents"[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: graph_extraction[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: rag_fusion[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: static_research_agent[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: summary[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: vision_pdf[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: hyde[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: static_rag_agent[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: system[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: rag[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: vision_img[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: chunk_enrichment[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: dynamic_rag_agent_xml_tooling[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: graph_entity_description[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: collection_summary[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading saved prompt: graph_communities[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading prompts from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading default prompt: chunk_enrichment from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\chunk_enrichment.yaml.[0m
2025-07-31 16:26:54 - [32mINFO[0m - Loading default prompt: collection_summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\collection_summary.yaml.[0m
2025-07-31 16:26:55 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent.yaml.[0m
2025-07-31 16:26:55 - [32mINFO[0m - Loading default prompt: dynamic_rag_agent_xml_tooling from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\dynamic_rag_agent_xml_tooling.yaml.[0m
2025-07-31 16:26:55 - [32mINFO[0m - Loading default prompt: graph_communities from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_communities.yaml.[0m
2025-07-31 16:26:55 - [32mINFO[0m - Loading default prompt: graph_entity_description from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_entity_description.yaml.[0m
2025-07-31 16:26:55 - [32mINFO[0m - Loading default prompt: graph_extraction from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\graph_extraction.yaml.[0m
2025-07-31 16:26:55 - [32mINFO[0m - Loading default prompt: hyde from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\hyde.yaml.[0m
2025-07-31 16:26:55 - [32mINFO[0m - Loading default prompt: rag from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag.yaml.[0m
2025-07-31 16:26:56 - [32mINFO[0m - Loading default prompt: rag_fusion from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\rag_fusion.yaml.[0m
2025-07-31 16:26:56 - [32mINFO[0m - Loading default prompt: static_rag_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_rag_agent.yaml.[0m
2025-07-31 16:26:56 - [32mINFO[0m - Loading default prompt: static_research_agent from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\static_research_agent.yaml.[0m
2025-07-31 16:26:56 - [32mINFO[0m - Loading default prompt: summary from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\summary.yaml.[0m
2025-07-31 16:26:56 - [32mINFO[0m - Loading default prompt: system from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\system.yaml.[0m
2025-07-31 16:26:56 - [32mINFO[0m - Loading default prompt: vision_img from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_img.yaml.[0m
2025-07-31 16:26:56 - [32mINFO[0m - Loading default prompt: vision_pdf from D:\work\2025AI_Application\rag֪ʶ��-�°���\anythingchat\backend\py\core\providers\database\prompts\vision_pdf.yaml.[0m
2025-07-31 16:27:05 - [32mINFO[0m - Using existing S3 bucket: rag[0m
2025-07-31 16:27:05 - [32mINFO[0m - Initializing OCRProvider with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='mistral' model='mistral-ocr-latest' concurrent_request_limit=256 max_retries=3 initial_backoff=1.0 max_backoff=64.0[0m
2025-07-31 16:27:05 - [33mWARNING[0m - MISTRAL_API_KEY not set in environment, if you plan to use Mistral OCR, please set it.[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initialized extra parser zerox for pdf[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initialized extra parser ocr for pdf[0m
2025-07-31 16:27:08 - [32mINFO[0m - R2RIngestionProvider initialized with config: app=AppConfig(extra_fields={}, project_name=None, user_tools_path=None, default_max_documents_per_user=10000, default_max_chunks_per_user=10000000, default_max_collections_per_user=5000, default_max_upload_size=214748364800, quality_llm='openai/volcengine/deepseek-v3', fast_llm='openai/volcengine/deepseek-v3', vlm='volcengine/doubao-seed-1.6', audio_lm='openai/whisper-1', reasoning_llm='openai/volcengine/deepseek-v3', planning_llm='openai/volcengine/deepseek-v3', max_upload_size_by_type={'txt': 2000000, 'md': 2000000, 'tsv': 2000000, 'csv': 5000000, 'html': 5000000, 'doc': 10000000, 'docx': 10000000, 'ppt': 20000000, 'pptx': 20000000, 'xls': 10000000, 'xlsx': 10000000, 'odt': 5000000, 'pdf': 30000000, 'eml': 5000000, 'msg': 5000000, 'p7s': 5000000, 'bmp': 5000000, 'heic': 5000000, 'jpeg': 5000000, 'jpg': 5000000, 'png': 5000000, 'tiff': 5000000, 'epub': 10000000, 'rtf': 5000000, 'rst': 5000000, 'org': 5000000}) extra_fields={} provider='r2r' excluded_parsers=[] chunking_strategy=<ChunkingStrategy.RECURSIVE: 'recursive'> chunk_size=1024 chunk_overlap=512 chunk_enrichment_settings=ChunkEnrichmentSettings(enable_chunk_enrichment=False, n_chunks=2, generation_config=None, chunk_enrichment_prompt='chunk_enrichment') extra_parsers={'pdf': ['zerox', 'ocr']} audio_transcription_model=None vlm=None vlm_batch_size=20 vlm_max_tokens_to_sample=1024 max_concurrent_vlm_tasks=20 vlm_ocr_one_page_per_chunk=True skip_document_summary=False document_summary_system_prompt='system' document_summary_task_prompt='summary' chunks_for_document_summary=128 document_summary_model=None parser_overrides={} automatic_extraction=True document_summary_max_length=100000 separator=None[0m
2025-07-31 16:27:08 - [32mINFO[0m - Default admin user already exists.[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initializing database maintenance service[0m
2025-07-31 16:27:08 - [32mINFO[0m - Scheduler started[0m
2025-07-31 16:27:08 - [32mINFO[0m - Scheduler started[0m
2025-07-31 16:27:08 - [32mINFO[0m - Adding job vacuum_database with trigger cron and kwargs {'minute': '0', 'hour': '3', 'day': '*', 'month': '*', 'day_of_week': '*', 'kwargs': {'full': False, 'analyze': True}}[0m
2025-07-31 16:27:08 - [32mINFO[0m - Added job "MaintenanceService.vacuum_database" to job store "default"[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initializing ChunksRouter[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initializing CollectionsRouter[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initializing ConversationsRouter[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initializing DocumentsRouter[0m
2025-07-31 16:27:08 - [32mINFO[0m - Initializing GraphRouter[0m
2025-07-31 16:27:09 - [32mINFO[0m - Initializing IndicesRouter[0m
2025-07-31 16:27:09 - [32mINFO[0m - Initializing PromptsRouter[0m
2025-07-31 16:27:09 - [32mINFO[0m - Initializing RetrievalRouter[0m
2025-07-31 16:27:09 - [32mINFO[0m - Initializing SystemRouter[0m
2025-07-31 16:27:09 - [32mINFO[0m - Initializing UsersRouter[0m
2025-07-31 16:27:09 - [32mINFO[0m - Logging is configured at INFO level.[0m
2025-07-31 16:27:09 - [32mINFO[0m - Started server process [30088][0m
2025-07-31 16:27:09 - [32mINFO[0m - Waiting for application startup.[0m
2025-07-31 16:27:09 - [32mINFO[0m - Application startup complete.[0m
2025-07-31 16:27:09 - [32mINFO[0m - Uvicorn running on http://0.0.0.0:7272 (Press CTRL+C to quit)[0m
2025-07-31 16:28:45 - [32mINFO[0m - 127.0.0.1:61619 - "POST /v3/users/login HTTP/1.1" [32m200[0m
2025-07-31 16:28:45 - [32mINFO[0m - 127.0.0.1:61619 - "OPTIONS /v3/users/me HTTP/1.1" [32m200[0m
2025-07-31 16:28:46 - [32mINFO[0m - 127.0.0.1:61619 - "GET /v3/users/me HTTP/1.1" [32m200[0m
2025-07-31 16:28:46 - [32mINFO[0m - 127.0.0.1:61619 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-31 16:28:47 - [32mINFO[0m - 127.0.0.1:61619 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-31 16:28:47 - [32mINFO[0m - 127.0.0.1:61620 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-31 16:28:47 - [32mINFO[0m - 127.0.0.1:61619 - "OPTIONS /v3/system/status HTTP/1.1" [32m200[0m
2025-07-31 16:28:48 - [32mINFO[0m - 127.0.0.1:61620 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-31 16:28:49 - [32mINFO[0m - 127.0.0.1:61619 - "GET /v3/system/status HTTP/1.1" [32m200[0m
2025-07-31 16:29:02 - [32mINFO[0m - 127.0.0.1:61660 - "OPTIONS /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:29:04 - [32mINFO[0m - 127.0.0.1:61660 - "GET /v3/documents?offset=0&limit=1000&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:29:58 - [32mINFO[0m - 127.0.0.1:61662 - "OPTIONS /v3/documents HTTP/1.1" [32m200[0m
2025-07-31 16:29:58 - [33mWARNING[0m - Skipping data after last boundary[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:00 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:01 - [32mINFO[0m - Initializing text splitter with method: ChunkingStrategy.RECURSIVE[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 1[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 8[0m
2025-07-31 16:30:15 - [32mINFO[0m - NewAPI embedding request - model: aliyun/text-embedding-v4, texts count: 7[0m
2025-07-31 16:30:32 - [32mINFO[0m - Successful ingestion for document_id: 6f29159f-fbbc-57d5-9fb4-a7189b06041d, with vector count: 111[0m
2025-07-31 16:30:34 - [33mWARNING[0m - Automatic extraction not yet implemented for `simple` ingestion workflows.[0m
2025-07-31 16:30:34 - [32mINFO[0m - 127.0.0.1:61662 - "POST /v3/documents HTTP/1.1" [32m202[0m
2025-07-31 16:31:15 - [32mINFO[0m - 127.0.0.1:62015 - "OPTIONS /v3/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-07-31 16:31:15 - [32mINFO[0m - 127.0.0.1:62016 - "OPTIONS /v3/users/2acb499e-8428-543b-bd85-0d9098718220/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-07-31 16:31:17 - [32mINFO[0m - 127.0.0.1:62015 - "GET /v3/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-07-31 16:31:17 - [32mINFO[0m - 127.0.0.1:62016 - "GET /v3/users/2acb499e-8428-543b-bd85-0d9098718220/collections?offset=0&limit=1000 HTTP/1.1" [32m200[0m
2025-07-31 16:31:31 - [32mINFO[0m - 127.0.0.1:62020 - "OPTIONS /v3/collections HTTP/1.1" [32m200[0m
2025-07-31 16:31:33 - [32mINFO[0m - 127.0.0.1:62020 - "POST /v3/collections HTTP/1.1" [32m200[0m
2025-07-31 16:31:37 - [32mINFO[0m - 127.0.0.1:62020 - "OPTIONS /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 HTTP/1.1" [32m200[0m
2025-07-31 16:31:38 - [32mINFO[0m - 127.0.0.1:62020 - "OPTIONS /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/documents?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:38 - [32mINFO[0m - 127.0.0.1:62104 - "OPTIONS /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/users?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:38 - [32mINFO[0m - 127.0.0.1:62103 - "OPTIONS /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/entities?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:38 - [32mINFO[0m - 127.0.0.1:62102 - "OPTIONS /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/relationships?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:38 - [32mINFO[0m - 127.0.0.1:62101 - "OPTIONS /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/communities?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:38 - [32mINFO[0m - 127.0.0.1:62100 - "OPTIONS /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/documents?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:38 - [32mINFO[0m - 127.0.0.1:62020 - "OPTIONS /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/users?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:39 - [32mINFO[0m - 127.0.0.1:62104 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 HTTP/1.1" [32m200[0m
2025-07-31 16:31:39 - [32mINFO[0m - 127.0.0.1:62103 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/documents?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:39 - [32mINFO[0m - 127.0.0.1:62103 - "OPTIONS /v3/documents//chunks?include_vectors=false&offset=0&limit=10 HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62102 - "GET /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/entities?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62100 - "GET /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/relationships?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62102 - "OPTIONS /v3/documents//entities?offset=0&limit=10&include_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62100 - "OPTIONS /v3/documents//relationships?offset=0&limit=10 HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [33mWARNING[0m - 127.0.0.1:62102 - "GET /v3/documents//chunks?include_vectors=false&offset=0&limit=10 HTTP/1.1" [33m404[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62101 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/users?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62020 - "GET /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/communities?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [33mWARNING[0m - 127.0.0.1:62100 - "GET /v3/documents//entities?offset=0&limit=10&include_embeddings=false HTTP/1.1" [33m404[0m
2025-07-31 16:31:40 - [33mWARNING[0m - 127.0.0.1:62102 - "GET /v3/documents//relationships?offset=0&limit=10 HTTP/1.1" [33m404[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62104 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/documents?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:31:40 - [32mINFO[0m - 127.0.0.1:62103 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 HTTP/1.1" [32m200[0m
2025-07-31 16:31:41 - [32mINFO[0m - 127.0.0.1:62101 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/users?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:32:03 - [32mINFO[0m - 127.0.0.1:62165 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 HTTP/1.1" [32m200[0m
2025-07-31 16:32:16 - [32mINFO[0m - 127.0.0.1:62167 - "OPTIONS /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/pull HTTP/1.1" [32m200[0m
2025-07-31 16:32:18 - [33mWARNING[0m - Document 6f29159f-fbbc-57d5-9fb4-a7189b06041d has no entities, extraction may not have been called, skipping.[0m
2025-07-31 16:32:18 - [33mWARNING[0m - Document 90bbe979-adc6-5bb9-8548-31309b4e8281 has no entities, extraction may not have been called, skipping.[0m
2025-07-31 16:32:18 - [33mWARNING[0m - No documents were added to graph 122fdf6a-e116-546b-a8f6-e4cb2e2c0a09, marking as failed.[0m
2025-07-31 16:32:18 - [32mINFO[0m - 127.0.0.1:62167 - "POST /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/pull HTTP/1.1" [32m200[0m
2025-07-31 16:35:00 - [32mINFO[0m - 127.0.0.1:62674 - "OPTIONS /v3/documents?offset=0&limit=100&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:35:01 - [32mINFO[0m - 127.0.0.1:62674 - "GET /v3/documents?offset=0&limit=100&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:35:05 - [32mINFO[0m - 127.0.0.1:62674 - "OPTIONS /v3/users?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:35:05 - [32mINFO[0m - 127.0.0.1:62674 - "GET /v3/users?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:35:11 - [32mINFO[0m - 127.0.0.1:62676 - "OPTIONS /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/communities/build HTTP/1.1" [32m200[0m
2025-07-31 16:35:12 - [32mINFO[0m - Running inline clustering for collection=122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 with params={}[0m
2025-07-31 16:35:12 - [32mINFO[0m - Clustering over 0 relationships for 122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 with settings: {}[0m
2025-07-31 16:35:12 - [31mERROR[0m - Error running orchestrated community building: No relationships found for clustering 

Attempting to run without orchestration.[0m
2025-07-31 16:35:12 - [32mINFO[0m - Running build-communities without orchestration.[0m
2025-07-31 16:35:13 - [32mINFO[0m - Running inline clustering for collection=122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 with params={}[0m
2025-07-31 16:35:13 - [32mINFO[0m - Clustering over 0 relationships for 122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 with settings: {}[0m
2025-07-31 16:35:13 - [33mWARNING[0m - 127.0.0.1:62676 - "POST /v3/graphs/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09/communities/build HTTP/1.1" [33m400[0m
2025-07-31 16:35:20 - [32mINFO[0m - 127.0.0.1:62710 - "GET /v3/collections/122fdf6a-e116-546b-a8f6-e4cb2e2c0a09 HTTP/1.1" [32m200[0m
2025-07-31 16:36:04 - [32mINFO[0m - 127.0.0.1:62711 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 16:36:04 - [32mINFO[0m - 127.0.0.1:62711 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 16:36:06 - [31mERROR[0m - 127.0.0.1:62711 - "OPTIONS /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:36:06 - [32mINFO[0m - 127.0.0.1:62711 - "OPTIONS /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:36:06 - [31mERROR[0m - 127.0.0.1:62711 - "GET /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:36:07 - [32mINFO[0m - 127.0.0.1:62829 - "GET /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:36:18 - [32mINFO[0m - 127.0.0.1:62830 - "GET /v3/users?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:36:25 - [32mINFO[0m - 127.0.0.1:62831 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-31 16:37:19 - [32mINFO[0m - 127.0.0.1:63106 - "OPTIONS /v3/users?offset=0&limit=1 HTTP/1.1" [32m200[0m
2025-07-31 16:37:19 - [32mINFO[0m - 127.0.0.1:63106 - "GET /v3/users?offset=0&limit=1 HTTP/1.1" [32m200[0m
2025-07-31 16:37:23 - [31mERROR[0m - 127.0.0.1:63106 - "GET /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:37:25 - [32mINFO[0m - 127.0.0.1:63107 - "GET /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:37:25 - [32mINFO[0m - 127.0.0.1:63106 - "GET /v3/documents?offset=0&limit=100&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:37:25 - [31mERROR[0m - 127.0.0.1:63131 - "GET /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:37:26 - [32mINFO[0m - 127.0.0.1:63134 - "GET /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:37:48 - [32mINFO[0m - 127.0.0.1:63221 - "OPTIONS /v3/conversations HTTP/1.1" [32m200[0m
2025-07-31 16:37:48 - [32mINFO[0m - 127.0.0.1:63221 - "POST /v3/conversations HTTP/1.1" [32m200[0m
2025-07-31 16:37:48 - [32mINFO[0m - 127.0.0.1:63221 - "OPTIONS /v3/retrieval/agent HTTP/1.1" [32m200[0m
2025-07-31 16:37:50 - [33mWARNING[0m - User tools directory not found: ../docker/user_tools[0m
2025-07-31 16:37:50 - [32mINFO[0m - 127.0.0.1:63221 - "POST /v3/retrieval/agent HTTP/1.1" [32m200[0m
2025-07-31 16:37:52 - [33mWARNING[0m - Warning: model not found. Using cl100k_base encoding.[0m
2025-07-31 16:37:52 - [33mWARNING[0m - Warning: model not found. Using cl100k_base encoding.[0m
2025-07-31 16:37:53 - [32mINFO[0m - 127.0.0.1:63221 - "OPTIONS /v3/conversations/4d725840-fd7b-4382-aedc-86f34e0b7288/messages HTTP/1.1" [32m200[0m
2025-07-31 16:37:54 - [32mINFO[0m - 127.0.0.1:63221 - "POST /v3/conversations/4d725840-fd7b-4382-aedc-86f34e0b7288/messages HTTP/1.1" [32m200[0m
2025-07-31 16:38:05 - [33mWARNING[0m - User tools directory not found: ../docker/user_tools[0m
2025-07-31 16:38:05 - [32mINFO[0m - 127.0.0.1:63222 - "POST /v3/retrieval/agent HTTP/1.1" [32m200[0m
2025-07-31 16:38:07 - [33mWARNING[0m - Warning: model not found. Using cl100k_base encoding.[0m
2025-07-31 16:38:07 - [33mWARNING[0m - Warning: model not found. Using cl100k_base encoding.[0m
2025-07-31 16:38:08 - [32mINFO[0m - 127.0.0.1:63222 - "POST /v3/conversations/4d725840-fd7b-4382-aedc-86f34e0b7288/messages HTTP/1.1" [32m200[0m
2025-07-31 16:38:54 - [33mWARNING[0m - User tools directory not found: ../docker/user_tools[0m
2025-07-31 16:38:54 - [32mINFO[0m - 127.0.0.1:63470 - "POST /v3/retrieval/agent HTTP/1.1" [32m200[0m
2025-07-31 16:39:02 - [33mWARNING[0m - Warning: model not found. Using cl100k_base encoding.[0m
2025-07-31 16:39:02 - [33mWARNING[0m - Warning: model not found. Using cl100k_base encoding.[0m
2025-07-31 16:39:03 - [32mINFO[0m - 127.0.0.1:63470 - "POST /v3/conversations/4d725840-fd7b-4382-aedc-86f34e0b7288/messages HTTP/1.1" [32m200[0m
2025-07-31 16:39:11 - [31mERROR[0m - 127.0.0.1:63471 - "GET /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:39:11 - [32mINFO[0m - 127.0.0.1:63516 - "GET /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:39:16 - [32mINFO[0m - 127.0.0.1:63516 - "GET /v3/users?offset=0&limit=1 HTTP/1.1" [32m200[0m
2025-07-31 16:39:19 - [32mINFO[0m - 127.0.0.1:63516 - "OPTIONS /v3/users/2acb499e-8428-543b-bd85-0d9098718220 HTTP/1.1" [32m200[0m
2025-07-31 16:39:20 - [32mINFO[0m - 127.0.0.1:63516 - "GET /v3/users/2acb499e-8428-543b-bd85-0d9098718220 HTTP/1.1" [32m200[0m
2025-07-31 16:39:24 - [32mINFO[0m - 127.0.0.1:63516 - "GET /v3/users?offset=0&limit=1 HTTP/1.1" [32m200[0m
2025-07-31 16:39:26 - [32mINFO[0m - 127.0.0.1:63516 - "OPTIONS /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-31 16:39:27 - [32mINFO[0m - 127.0.0.1:63516 - "GET /v3/system/settings HTTP/1.1" [32m200[0m
2025-07-31 16:40:26 - [32mINFO[0m - 127.0.0.1:63729 - "GET /v3/users?offset=0&limit=1 HTTP/1.1" [32m200[0m
2025-07-31 16:40:28 - [31mERROR[0m - 127.0.0.1:63729 - "GET /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:40:28 - [32mINFO[0m - 127.0.0.1:63730 - "GET /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:40:31 - [31mERROR[0m - 127.0.0.1:63730 - "GET /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:40:31 - [32mINFO[0m - 127.0.0.1:63729 - "GET /v3/documents?offset=0&limit=100&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:40:31 - [32mINFO[0m - 127.0.0.1:63742 - "GET /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:40:43 - [31mERROR[0m - 127.0.0.1:63786 - "GET /v3/conversations?offset=0&limit=[31m500[0m HTTP/1.1" [32m200[0m
2025-07-31 16:40:43 - [32mINFO[0m - 127.0.0.1:63787 - "GET /v3/documents?offset=0&limit=100&include_summary_embeddings=false HTTP/1.1" [32m200[0m
2025-07-31 16:40:43 - [32mINFO[0m - 127.0.0.1:63788 - "GET /v3/collections?offset=0&limit=100 HTTP/1.1" [32m200[0m
2025-07-31 16:45:43 - [32mINFO[0m - 127.0.0.1:64617 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 16:50:45 - [32mINFO[0m - 127.0.0.1:65207 - "OPTIONS /v3/users/refresh-token HTTP/1.1" [32m200[0m
2025-07-31 16:50:46 - [32mINFO[0m - 127.0.0.1:65207 - "POST /v3/users/refresh-token HTTP/1.1" [32m200[0m
