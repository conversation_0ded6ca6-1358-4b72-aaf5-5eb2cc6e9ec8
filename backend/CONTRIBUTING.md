# R2R Contribution Guide

## Quick Start

- **Pre-Discussion**: Feel free to propose your ideas via issues, [Discord](https://discord.gg/p6KqD2kjtB) if you want to get early feedback.
- **Code of Conduct**: Adhere to our [Code of Conduct](./CODE_OF_CONDUCT.md) in all interactions.
- **Pull Requests (PRs)**: Follow the PR process for contributions.

## Pull Request Process

1. **Dependencies**: Ensure all dependencies are necessary and documented.
2. **Documentation**: Update README.md with any changes to interfaces, including new environment variables, exposed ports, and other relevant details.
3. **Versioning**: Increment version numbers in examples and README.md following [SemVer](http://semver.org/).
4. **Review**: A PR can be merged after receiving approval from at least two other developers. If you lack merge permissions, request a review for merging.

## Attribution

This Code of Conduct adapts from the [Contributor Covenant, version 1.4](http://contributor-covenant.org/version/1/4/).
