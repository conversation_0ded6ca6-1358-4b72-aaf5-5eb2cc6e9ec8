repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.0.0
    hooks:
      - id: trailing-whitespace
        exclude: ^.venv/
      - id: end-of-file-fixer
        exclude: ^.venv/
      - id: check-added-large-files
        exclude: ^.venv/
      - id: check-ast
        exclude: ^.venv/
      - id: check-yaml
        exclude: ^(.venv/|deployment/)

  - repo: local
    hooks:
      - id: check-typing-imports
        name: Check for Dict, List, or Union usage
        entry: bash -c 'echo "Checking for typing imports..." && FOUND=$(cd "$(git rev-parse --show-toplevel)" && find . -path "*/py/*.py" | grep -v "venv" | grep -v "/.venv/" | grep -v "/site-packages/" | grep -v "test_" | grep -v "/migrations/" | xargs grep -l "from typing.*import.*[^d]Dict\\|from typing.*import.*List\\|from typing.*import.*Union" 2>/dev/null || echo "") && if [ -n "$FOUND" ]; then echo "$FOUND"; echo "  Please import dict instead of Dict, list instead of List, and the logical OR operator"; exit 1; else echo "No problematic imports found!"; exit 0; fi'
        language: system
        types: [python]
        pass_filenames: false

  - repo: local
    hooks:
      - id: check-print-statements
        name: Check for print statements
        entry: bash -c 'echo "Checking for print statements..." && FOUND=$(cd "$(git rev-parse --show-toplevel)" && find . -path "*/py/*.py" | grep -v "venv" | grep -v "/.venv/" | grep -v "/site-packages/" | grep -v "test_" | grep -v "/core/examples/" | grep -v "/migrations/" | grep -v "/tests/" | grep -v "/examples.py" | xargs grep -l "print(" 2>/dev/null || echo "") && if [ -n "$FOUND" ]; then echo "$FOUND"; echo "Found print statements!"; exit 1; else echo "No print statements found!"; exit 0; fi'
        language: system
        types: [python]
        pass_filenames: false
        exclude: ^(.venv/|py/.venv/|py/core/examples/|py/migrations/|py/tests/)

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.9.6
    hooks:
      - id: ruff
        args: [--fix]
        files: ^py/
        exclude: ^(py/tests/|.venv/)
      - id: ruff-format
        files: ^py/
        exclude: ^(py/tests/|.venv/)

  - repo: local
    hooks:
      - id: mypy
        name: mypy
        entry: bash -c 'cd "$(git rev-parse --show-toplevel)/py" && python -m mypy --exclude "migrations" --exclude "venv*" --exclude "test_*" .'
        language: system
        types: [python]
        pass_filenames: false
        exclude: ^(.venv/|migrations/)
