# 尝试不同的请求格式
$body1 = '"测试文本"'
$body2 = '{"text": "测试文本"}'
$body3 = 'text=测试文本'
$uri = "http://localhost:7272/v3/retrieval/embedding"
$headers = @{
    "Content-Type" = "application/json"
}

try {
    Write-Host "🔄 正在测试embedding接口..."
    Write-Host "📍 URL: $uri"

    # 尝试格式1: 纯字符串
    Write-Host "📝 尝试格式1: $body1"
    try {
        $response = Invoke-RestMethod -Uri $uri -Method POST -Headers $headers -Body $body1
        Write-Host "✅ 格式1成功!"
    } catch {
        Write-Host "❌ 格式1失败: $($_.Exception.Message)"

        # 尝试格式2: JSON对象
        Write-Host "📝 尝试格式2: $body2"
        try {
            $response = Invoke-RestMethod -Uri $uri -Method POST -Headers $headers -Body $body2
            Write-Host "✅ 格式2成功!"
        } catch {
            Write-Host "❌ 格式2失败: $($_.Exception.Message)"

            # 尝试格式3: 表单数据
            Write-Host "📝 尝试格式3: $body3"
            $headers["Content-Type"] = "application/x-www-form-urlencoded"
            $response = Invoke-RestMethod -Uri $uri -Method POST -Headers $headers -Body $body3
            Write-Host "✅ 格式3成功!"
        }
    }
    
    Write-Host "✅ Embedding调用成功!"
    Write-Host "📊 响应数据:"
    $response | ConvertTo-Json -Depth 3
    
    if ($response.results -and $response.results.embedding) {
        $embedding = $response.results.embedding
        Write-Host "🎯 向量维度: $($embedding.Count)"
        Write-Host "🔢 前5个值: $($embedding[0..4] -join ', ')"
    }
    
} catch {
    Write-Host "❌ Embedding调用失败:"
    Write-Host "   错误信息: $($_.Exception.Message)"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "   响应内容: $responseBody"
    }
}
