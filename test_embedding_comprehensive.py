#!/usr/bin/env python3
"""
全面测试嵌入模型修复效果的脚本
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加backend路径到sys.path
backend_path = Path(__file__).parent / "backend" / "py"
sys.path.insert(0, str(backend_path))

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["OPENAI_BASE_URL"] = "http://172.17.9.46:3000/v1"
os.environ["ALIYUN_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"

print("🔧 全面测试嵌入模型修复效果...")
print(f"📍 Backend路径: {backend_path}")
print(f"🔑 API密钥: {os.environ['OPENAI_API_KEY'][:20]}...")
print(f"🌐 API地址: {os.environ['OPENAI_BASE_URL']}")

def test_provider_import():
    """测试Provider导入"""
    print("\n📦 测试1: Provider导入测试")
    try:
        from core.providers.embeddings import NewAPIEmbeddingProvider
        print("✅ NewAPIEmbeddingProvider导入成功")
        return True
    except ImportError as e:
        print(f"❌ NewAPIEmbeddingProvider导入失败: {e}")
        return False

def test_config_creation():
    """测试配置创建"""
    print("\n⚙️ 测试2: 配置创建测试")
    try:
        from core.base.providers.embedding import EmbeddingConfig
        
        # 测试基本配置
        config_data = {
            "provider": "newapi",
            "base_model": "aliyun/text-embedding-v4",
            "base_dimension": 1024,
            "batch_size": 128,
            "concurrent_request_limit": 256,
            "api_base": "http://172.17.9.46:3000/v1"
        }
        
        config = EmbeddingConfig.create(**config_data)
        print("✅ 配置创建成功")
        print(f"   - Provider: {config.provider}")
        print(f"   - Model: {config.base_model}")
        print(f"   - Dimension: {config.base_dimension}")
        
        return config
    except Exception as e:
        print(f"❌ 配置创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_provider_initialization(config):
    """测试Provider初始化"""
    print("\n🚀 测试3: Provider初始化测试")
    try:
        from core.providers.embeddings import NewAPIEmbeddingProvider
        
        provider = NewAPIEmbeddingProvider(config)
        print("✅ NewAPIEmbeddingProvider初始化成功")
        print(f"   - 模型: {provider.base_model}")
        print(f"   - 维度: {provider.base_dimension}")
        print(f"   - API Base: {provider.api_base}")
        print(f"   - API Key: {provider.api_key[:20] if provider.api_key else 'None'}...")
        
        return provider
    except Exception as e:
        print(f"❌ Provider初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_factory_creation():
    """测试工厂类创建"""
    print("\n🏭 测试4: 工厂类创建测试")
    try:
        from core.main.assembly.factory import R2RProviderFactory
        from core.base.providers.embedding import EmbeddingConfig
        
        config_data = {
            "provider": "newapi",
            "base_model": "aliyun/text-embedding-v4",
            "base_dimension": 1024,
            "api_base": "http://172.17.9.46:3000/v1"
        }
        
        config = EmbeddingConfig.create(**config_data)
        provider = R2RProviderFactory.create_embedding_provider(config)
        
        print("✅ 工厂类创建Provider成功")
        print(f"   - Provider类型: {type(provider).__name__}")
        
        return provider
    except Exception as e:
        print(f"❌ 工厂类创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None

async def test_embedding_call(provider):
    """测试实际的嵌入调用"""
    print("\n🎯 测试5: 嵌入调用测试")
    try:
        test_text = "这是一个测试文本"
        
        # 测试单个文本嵌入
        print("   测试单个文本嵌入...")
        embedding = await provider.async_get_embedding(test_text)
        print(f"✅ 单个文本嵌入成功")
        print(f"   - 向量维度: {len(embedding)}")
        print(f"   - 前3个值: {embedding[:3]}")
        
        # 测试批量文本嵌入
        print("   测试批量文本嵌入...")
        test_texts = ["测试文本1", "测试文本2", "测试文本3"]
        embeddings = await provider.async_get_embeddings(test_texts)
        print(f"✅ 批量文本嵌入成功")
        print(f"   - 嵌入数量: {len(embeddings)}")
        print(f"   - 每个向量维度: {len(embeddings[0])}")
        
        return True
    except Exception as e:
        print(f"❌ 嵌入调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_litellm_comparison():
    """测试LiteLLM对比"""
    print("\n🔄 测试6: LiteLLM对比测试")
    try:
        from core.base.providers.embedding import EmbeddingConfig
        from core.providers.embeddings import LiteLLMEmbeddingProvider
        
        # 创建LiteLLM配置
        litellm_config_data = {
            "provider": "litellm",
            "base_model": "aliyun/text-embedding-v4",
            "base_dimension": 1024,
        }
        
        # 添加api_base到extra_fields
        litellm_config = EmbeddingConfig.create(**litellm_config_data)
        litellm_config.extra_fields = {"api_base": "http://172.17.9.46:3000/v1"}
        
        litellm_provider = LiteLLMEmbeddingProvider(litellm_config)
        print("✅ LiteLLM Provider创建成功")
        
        # 比较配置处理
        kwargs = litellm_provider._get_embedding_kwargs()
        print("📋 LiteLLM生成的kwargs:")
        for key, value in kwargs.items():
            if key == 'api_key' and value:
                print(f"   - {key}: {value[:20]}...")
            else:
                print(f"   - {key}: {value}")
        
        return True
    except Exception as e:
        print(f"❌ LiteLLM对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🎯 开始全面嵌入模型测试\n")
    
    results = []
    
    # 测试1: Provider导入
    results.append(test_provider_import())
    
    # 测试2: 配置创建
    config = test_config_creation()
    results.append(config is not None)
    
    if config:
        # 测试3: Provider初始化
        provider = test_provider_initialization(config)
        results.append(provider is not None)
        
        # 测试4: 工厂类创建
        factory_provider = test_factory_creation()
        results.append(factory_provider is not None)
        
        # 测试5: 嵌入调用（如果Provider初始化成功）
        if provider:
            embedding_result = await test_embedding_call(provider)
            results.append(embedding_result)
        else:
            results.append(False)
    else:
        results.extend([False, False, False])
    
    # 测试6: LiteLLM对比
    results.append(test_litellm_comparison())
    
    # 总结结果
    print(f"\n📊 测试结果总结:")
    test_names = [
        "Provider导入",
        "配置创建", 
        "Provider初始化",
        "工厂类创建",
        "嵌入调用",
        "LiteLLM对比"
    ]
    
    passed = sum(results)
    total = len(results)
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅" if result else "❌"
        print(f"   {status} 测试{i+1}: {name}")
    
    print(f"\n🎉 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎊 所有测试通过！NewAPI Provider修复成功！")
        print("\n💡 建议:")
        print("   1. 重启R2R服务以应用修复")
        print("   2. 监控生产环境中的嵌入调用")
        print("   3. 考虑添加更多的错误处理和监控")
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，还有少量问题需要解决")
    else:
        print("❌ 多个测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
