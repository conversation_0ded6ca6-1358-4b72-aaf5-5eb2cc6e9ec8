# AnythingChat 配置文件清单

## 配置文件位置和用途

### 核心配置文件

| 文件路径 | 文件类型 | 用途 | 优先级 |
|---------|---------|------|--------|
| `backend/py/r2r/r2r.toml` | TOML | 默认系统配置 | 低 |
| `backend/.env.example` | ENV | 环境变量模板 | - |
| `backend/r2r-full.env` | ENV | Docker环境变量 | 高 |
| `backend/py/all_possible_config.toml` | TOML | 完整配置示例 | - |

### 预设配置文件

| 文件路径 | 用途 | 特点 |
|---------|------|------|
| `backend/py/core/configs/full.toml` | 完整功能配置 | 包含所有高级功能 |
| `backend/py/core/configs/full_azure.toml` | Azure配置 | 使用Azure OpenAI服务 |
| `backend/py/core/configs/full_ollama.toml` | Ollama配置 | 使用本地Ollama模型 |
| `backend/py/core/configs/full_lm_studio.toml` | LM Studio配置 | 使用本地LM Studio |
| `backend/py/core/configs/r2r_azure.toml` | 简化Azure配置 | 基础Azure配置 |
| `backend/py/core/configs/r2r_with_auth.toml` | 认证配置 | 启用用户认证 |
| `backend/py/core/configs/ollama.toml` | 简化Ollama配置 | 基础Ollama配置 |
| `backend/py/core/configs/lm_studio.toml` | 简化LM Studio配置 | 基础LM Studio配置 |
| `backend/py/core/configs/gemini.toml` | Gemini配置 | 使用Google Gemini |
| `backend/py/core/configs/tavily.toml` | Tavily配置 | 启用网络搜索功能 |

## 配置项分类

### 1. 外部MinIO存储配置

#### 配置文件位置
- **主配置**: `[file]` 部分
- **环境变量**: `S3_*`, `AWS_*` 前缀

#### 关键配置项
```toml
[file]
provider = "s3"
bucket_name = "your-bucket-name"
aws_access_key_id = "your-access-key"
aws_secret_access_key = "your-secret-key"
region_name = "us-east-1"
endpoint_url = "http://localhost:9000"  # MinIO服务器地址
```

#### 环境变量
```bash
S3_BUCKET_NAME=your-bucket-name
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
S3_ENDPOINT_URL=http://localhost:9000
```

### 2. PostgreSQL数据库配置

#### 配置文件位置
- **主配置**: `[database]` 部分
- **环境变量**: `R2R_POSTGRES_*` 前缀

#### 关键配置项
```toml
[database]
provider = "postgres"
user = "postgres"
password = "your-password"
host = "localhost"
port = 5432
db_name = "r2r"
project_name = "anythingchat"

[database.postgres_configuration_settings]
max_connections = 256
statement_cache_size = 100
shared_buffers = 16384
work_mem = 4096
```

#### 环境变量
```bash
R2R_POSTGRES_USER=postgres
R2R_POSTGRES_PASSWORD=your-password
R2R_POSTGRES_HOST=localhost
R2R_POSTGRES_PORT=5432
R2R_POSTGRES_DBNAME=r2r
R2R_PROJECT_NAME=anythingchat
R2R_POSTGRES_MAX_CONNECTIONS=1024
R2R_POSTGRES_STATEMENT_CACHE_SIZE=100
```

### 3. 大模型API调用配置

#### 配置文件位置
- **模型选择**: `[app]` 部分
- **生成配置**: `[completion]` 部分
- **嵌入配置**: `[embedding]` 部分
- **环境变量**: 各提供商特定前缀

#### 关键配置项
```toml
[app]
fast_llm = "openai/gpt-4.1-mini"
quality_llm = "openai/gpt-4.1"
vlm = "openai/gpt-4.1"
audio_lm = "openai/whisper-1"
reasoning_llm = "openai/o3-mini"
planning_llm = "anthropic/claude-3-7-sonnet-20250219"

[completion]
provider = "r2r"
concurrent_request_limit = 64
request_timeout = 60
max_retries = 3

[completion.generation_config]
temperature = 0.1
top_p = 1
max_tokens_to_sample = 4_096
stream = false

[embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-small"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256
```

#### 环境变量
```bash
# OpenAI
OPENAI_API_KEY=sk-your-openai-key
OPENAI_API_BASE=https://api.openai.com/v1

# Anthropic
ANTHROPIC_API_KEY=your-anthropic-key

# Azure OpenAI
AZURE_API_KEY=your-azure-key
AZURE_API_BASE=https://your-resource.openai.azure.com
AZURE_API_VERSION=2024-02-15-preview

# Google
GEMINI_API_KEY=your-gemini-key
GOOGLE_APPLICATION_CREDENTIALS=/path/to/creds.json

# 其他提供商
MISTRAL_API_KEY=your-mistral-key
GROQ_API_KEY=your-groq-key
COHERE_API_KEY=your-cohere-key
XAI_API_KEY=your-xai-key

# 本地部署
OLLAMA_API_BASE=http://localhost:11434
LM_STUDIO_API_BASE=http://localhost:1234
LM_STUDIO_API_KEY=1234
```

## 配置加载优先级

1. **环境变量** (最高优先级)
2. **指定配置文件** (`R2R_CONFIG_PATH`)
3. **预设配置** (`R2R_CONFIG_NAME`)
4. **默认配置** (`r2r.toml`)

## 配置验证命令

```bash
# 检查配置加载
python -c "from r2r import R2RConfig; config = R2RConfig.load(); print('配置加载成功')"

# 查看当前配置
python -c "from r2r import R2RConfig; print(R2RConfig.load().to_toml())"

# 测试数据库连接
python -c "
import asyncio
import asyncpg
async def test():
    conn = await asyncpg.connect('postgresql://user:pass@host:port/db')
    await conn.close()
    print('数据库连接成功')
asyncio.run(test())
"

# 测试S3连接
python -c "
import boto3
s3 = boto3.client('s3', 
    endpoint_url='http://localhost:9000',
    aws_access_key_id='minioadmin',
    aws_secret_access_key='minioadmin')
print(s3.list_buckets())
"

# 测试API调用
python -c "
import openai
client = openai.OpenAI()
print(client.models.list())
"
```

## 常用配置模板

### 开发环境
```bash
export R2R_CONFIG_NAME=default
export R2R_PROJECT_NAME=anythingchat_dev
export R2R_POSTGRES_USER=postgres
export R2R_POSTGRES_PASSWORD=postgres
export R2R_POSTGRES_HOST=localhost
export R2R_POSTGRES_PORT=5432
export R2R_POSTGRES_DBNAME=r2r
export OPENAI_API_KEY=sk-your-key
```

### 生产环境
```bash
export R2R_CONFIG_NAME=full
export R2R_PROJECT_NAME=anythingchat_prod
export R2R_POSTGRES_USER=prod_user
export R2R_POSTGRES_PASSWORD=secure_password
export R2R_POSTGRES_HOST=prod-db.example.com
export R2R_POSTGRES_PORT=5432
export R2R_POSTGRES_DBNAME=anythingchat
export S3_BUCKET_NAME=anythingchat-prod
export AWS_ACCESS_KEY_ID=your-access-key
export AWS_SECRET_ACCESS_KEY=your-secret-key
export OPENAI_API_KEY=sk-your-prod-key
```

### Docker部署
```bash
export R2R_CONFIG_NAME=full
export R2R_PROJECT_NAME=anythingchat
export R2R_POSTGRES_HOST=postgres
export R2R_POSTGRES_USER=postgres
export R2R_POSTGRES_PASSWORD=postgres
export R2R_POSTGRES_DBNAME=postgres
export S3_ENDPOINT_URL=http://minio:9000
export AWS_ACCESS_KEY_ID=minioadmin
export AWS_SECRET_ACCESS_KEY=minioadmin
```

## 配置文件示例

### 最小配置 (minimal.toml)
```toml
[app]
fast_llm = "openai/gpt-4.1-mini"
quality_llm = "openai/gpt-4.1-mini"

[completion]
provider = "r2r"

[embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-small"
base_dimension = 512

[database]
provider = "postgres"

[file]
provider = "postgres"

[auth]
provider = "r2r"
require_authentication = false
```

### 完整生产配置 (production.toml)
```toml
[app]
project_name = "anythingchat_prod"
fast_llm = "openai/gpt-4.1-mini"
quality_llm = "openai/gpt-4.1"
vlm = "openai/gpt-4.1"
audio_lm = "openai/whisper-1"
reasoning_llm = "openai/o3-mini"
planning_llm = "anthropic/claude-3-7-sonnet-20250219"

[completion]
provider = "r2r"
concurrent_request_limit = 128
request_timeout = 30

[embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-large"
base_dimension = 3072
concurrent_request_limit = 512

[database]
provider = "postgres"
[database.postgres_configuration_settings]
max_connections = 512
statement_cache_size = 200

[file]
provider = "s3"

[auth]
provider = "r2r"
require_authentication = true
require_email_verification = true
access_token_lifetime_in_minutes = 60
refresh_token_lifetime_in_days = 7

[ingestion]
provider = "r2r"
automatic_extraction = true
vlm_batch_size = 20

[orchestration]
provider = "simple"
kg_creation_concurrency_limit = 32
ingestion_concurrency_limit = 16
```

---

此清单提供了 AnythingChat 所有配置文件的完整概览，帮助您快速定位和修改所需的配置项。
