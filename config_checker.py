#!/usr/bin/env python3
"""
AnythingChat 配置检查脚本
用于验证系统配置是否正确设置
"""

import os
import sys
import asyncio
import json
from typing import Dict, List, Optional, Tuple
import subprocess

def check_environment_variables() -> Dict[str, bool]:
    """检查必需的环境变量"""
    required_vars = {
        # 数据库配置
        'R2R_POSTGRES_USER': os.getenv('R2R_POSTGRES_USER'),
        'R2R_POSTGRES_PASSWORD': os.getenv('R2R_POSTGRES_PASSWORD'),
        'R2R_POSTGRES_HOST': os.getenv('R2R_POSTGRES_HOST'),
        'R2R_POSTGRES_PORT': os.getenv('R2R_POSTGRES_PORT'),
        'R2R_POSTGRES_DBNAME': os.getenv('R2R_POSTGRES_DBNAME'),
        'R2R_PROJECT_NAME': os.getenv('R2R_PROJECT_NAME'),
    }
    
    optional_vars = {
        # LLM API密钥
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY'),
        'ANTHROPIC_API_KEY': os.getenv('ANTHROPIC_API_KEY'),
        'AZURE_API_KEY': os.getenv('AZURE_API_KEY'),
        'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY'),
        
        # 存储配置
        'S3_BUCKET_NAME': os.getenv('S3_BUCKET_NAME'),
        'AWS_ACCESS_KEY_ID': os.getenv('AWS_ACCESS_KEY_ID'),
        'AWS_SECRET_ACCESS_KEY': os.getenv('AWS_SECRET_ACCESS_KEY'),
        'S3_ENDPOINT_URL': os.getenv('S3_ENDPOINT_URL'),
        
        # 系统配置
        'R2R_CONFIG_NAME': os.getenv('R2R_CONFIG_NAME'),
        'R2R_CONFIG_PATH': os.getenv('R2R_CONFIG_PATH'),
    }
    
    results = {}
    
    print("🔍 检查必需环境变量...")
    for var, value in required_vars.items():
        is_set = value is not None and value.strip() != ''
        results[var] = is_set
        status = "✅" if is_set else "❌"
        print(f"  {status} {var}: {'已设置' if is_set else '未设置'}")
    
    print("\n🔍 检查可选环境变量...")
    for var, value in optional_vars.items():
        is_set = value is not None and value.strip() != ''
        results[var] = is_set
        status = "✅" if is_set else "⚠️"
        print(f"  {status} {var}: {'已设置' if is_set else '未设置'}")
    
    return results

def check_config_files() -> Dict[str, bool]:
    """检查配置文件是否存在"""
    config_files = {
        'default_config': 'backend/py/r2r/r2r.toml',
        'env_example': 'backend/.env.example',
        'docker_env': 'backend/r2r-full.env',
        'full_config': 'backend/py/core/configs/full.toml',
    }
    
    results = {}
    print("\n📁 检查配置文件...")
    
    for name, path in config_files.items():
        exists = os.path.exists(path)
        results[name] = exists
        status = "✅" if exists else "❌"
        print(f"  {status} {name}: {path} {'存在' if exists else '不存在'}")
    
    return results

async def check_database_connection() -> bool:
    """检查数据库连接"""
    print("\n🗄️ 检查数据库连接...")
    
    try:
        import asyncpg
        
        user = os.getenv('R2R_POSTGRES_USER')
        password = os.getenv('R2R_POSTGRES_PASSWORD')
        host = os.getenv('R2R_POSTGRES_HOST', 'localhost')
        port = os.getenv('R2R_POSTGRES_PORT', '5432')
        database = os.getenv('R2R_POSTGRES_DBNAME')
        
        if not all([user, password, database]):
            print("  ❌ 数据库环境变量未完整设置")
            return False
        
        connection_string = f"postgresql://{user}:{password}@{host}:{port}/{database}"
        
        conn = await asyncpg.connect(connection_string)
        await conn.execute("SELECT 1")
        await conn.close()
        
        print("  ✅ 数据库连接成功")
        return True
        
    except ImportError:
        print("  ⚠️ asyncpg 未安装，跳过数据库连接测试")
        return False
    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")
        return False

def check_s3_connection() -> bool:
    """检查S3/MinIO连接"""
    print("\n🗂️ 检查S3/MinIO连接...")
    
    try:
        import boto3
        from botocore.exceptions import ClientError
        
        bucket_name = os.getenv('S3_BUCKET_NAME')
        access_key = os.getenv('AWS_ACCESS_KEY_ID')
        secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')
        endpoint_url = os.getenv('S3_ENDPOINT_URL')
        region = os.getenv('AWS_REGION', 'us-east-1')
        
        if not all([bucket_name, access_key, secret_key]):
            print("  ⚠️ S3环境变量未设置，跳过S3连接测试")
            return False
        
        s3_client = boto3.client(
            's3',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            region_name=region,
            endpoint_url=endpoint_url
        )
        
        # 测试连接
        s3_client.head_bucket(Bucket=bucket_name)
        print("  ✅ S3/MinIO连接成功")
        return True
        
    except ImportError:
        print("  ⚠️ boto3 未安装，跳过S3连接测试")
        return False
    except ClientError as e:
        error_code = e.response.get('Error', {}).get('Code')
        if error_code == '404':
            print(f"  ❌ 存储桶 '{bucket_name}' 不存在")
        else:
            print(f"  ❌ S3连接失败: {e}")
        return False
    except Exception as e:
        print(f"  ❌ S3连接失败: {e}")
        return False

def check_llm_api() -> Dict[str, bool]:
    """检查LLM API连接"""
    print("\n🤖 检查LLM API连接...")
    
    results = {}
    
    # 检查OpenAI
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        try:
            import openai
            client = openai.OpenAI(api_key=openai_key)
            models = client.models.list()
            print("  ✅ OpenAI API连接成功")
            results['openai'] = True
        except ImportError:
            print("  ⚠️ openai 包未安装，跳过OpenAI测试")
            results['openai'] = False
        except Exception as e:
            print(f"  ❌ OpenAI API连接失败: {e}")
            results['openai'] = False
    else:
        print("  ⚠️ OPENAI_API_KEY 未设置")
        results['openai'] = False
    
    # 检查Anthropic
    anthropic_key = os.getenv('ANTHROPIC_API_KEY')
    if anthropic_key:
        try:
            import anthropic
            client = anthropic.Anthropic(api_key=anthropic_key)
            # 简单的API调用测试
            print("  ✅ Anthropic API密钥已设置")
            results['anthropic'] = True
        except ImportError:
            print("  ⚠️ anthropic 包未安装，跳过Anthropic测试")
            results['anthropic'] = False
        except Exception as e:
            print(f"  ❌ Anthropic API测试失败: {e}")
            results['anthropic'] = False
    else:
        print("  ⚠️ ANTHROPIC_API_KEY 未设置")
        results['anthropic'] = False
    
    return results

def check_r2r_config() -> bool:
    """检查R2R配置加载"""
    print("\n⚙️ 检查R2R配置加载...")
    
    try:
        # 尝试导入R2R配置
        sys.path.append('backend/py')
        from core.main.config import R2RConfig
        
        config = R2RConfig.load()
        print("  ✅ R2R配置加载成功")
        
        # 检查关键配置项
        if hasattr(config, 'app') and config.app:
            print(f"    - 项目名称: {getattr(config.app, 'project_name', '未设置')}")
            print(f"    - 快速LLM: {getattr(config.app, 'fast_llm', '未设置')}")
            print(f"    - 质量LLM: {getattr(config.app, 'quality_llm', '未设置')}")
        
        if hasattr(config, 'database') and config.database:
            print(f"    - 数据库提供商: {getattr(config.database, 'provider', '未设置')}")
        
        if hasattr(config, 'file') and config.file:
            print(f"    - 文件存储提供商: {getattr(config.file, 'provider', '未设置')}")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ 无法导入R2R配置: {e}")
        return False
    except Exception as e:
        print(f"  ❌ R2R配置加载失败: {e}")
        return False

def generate_report(results: Dict) -> None:
    """生成配置检查报告"""
    print("\n" + "="*50)
    print("📊 配置检查报告")
    print("="*50)
    
    # 统计结果
    total_checks = 0
    passed_checks = 0
    
    for category, items in results.items():
        if isinstance(items, dict):
            for item, status in items.items():
                total_checks += 1
                if status:
                    passed_checks += 1
        else:
            total_checks += 1
            if items:
                passed_checks += 1
    
    success_rate = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 配置状态良好！")
    elif success_rate >= 60:
        print("⚠️ 配置基本可用，建议完善部分配置")
    else:
        print("❌ 配置存在问题，需要修复")
    
    # 提供建议
    print("\n💡 配置建议:")
    
    env_vars = results.get('env_vars', {})
    if not env_vars.get('R2R_POSTGRES_USER'):
        print("  - 设置数据库环境变量 (R2R_POSTGRES_*)")
    
    if not any([env_vars.get('OPENAI_API_KEY'), env_vars.get('ANTHROPIC_API_KEY')]):
        print("  - 至少设置一个LLM API密钥")
    
    if not env_vars.get('S3_BUCKET_NAME') and env_vars.get('R2R_CONFIG_NAME') == 'full':
        print("  - 考虑配置S3存储或使用postgres文件存储")
    
    config_files = results.get('config_files', {})
    if not config_files.get('default_config'):
        print("  - 确保默认配置文件存在")

async def main():
    """主函数"""
    print("🚀 AnythingChat 配置检查工具")
    print("="*50)
    
    results = {}
    
    # 检查环境变量
    results['env_vars'] = check_environment_variables()
    
    # 检查配置文件
    results['config_files'] = check_config_files()
    
    # 检查数据库连接
    results['database'] = await check_database_connection()
    
    # 检查S3连接
    results['s3'] = check_s3_connection()
    
    # 检查LLM API
    results['llm_apis'] = check_llm_api()
    
    # 检查R2R配置
    results['r2r_config'] = check_r2r_config()
    
    # 生成报告
    generate_report(results)
    
    # 保存结果到文件
    with open('config_check_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 详细结果已保存到: config_check_results.json")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️ 检查已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 检查过程中出现错误: {e}")
        sys.exit(1)
