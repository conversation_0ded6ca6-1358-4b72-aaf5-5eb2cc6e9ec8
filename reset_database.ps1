# Reset R2R Graph Database Tables for 1024 Dimensions
Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "🔧 R2R Graph Database Reset Tool" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Yellow

Write-Host "`n📋 This script will:" -ForegroundColor Cyan
Write-Host "  1. Drop existing graph tables with wrong dimensions" -ForegroundColor White
Write-Host "  2. Allow R2R to recreate them with 1024 dimensions" -ForegroundColor White
Write-Host "  3. Preserve your document and chunk data" -ForegroundColor White

# Confirm action
$response = Read-Host "`n⚠️  Continue with database reset? (y/N)"
if ($response.ToLower() -ne 'y') {
    Write-Host "❌ Operation cancelled." -ForegroundColor Red
    exit 0
}

Write-Host "`n🔄 Starting database reset..." -ForegroundColor Green

try {
    # Check if psql is available
    $psqlPath = Get-Command psql -ErrorAction SilentlyContinue
    if (-not $psqlPath) {
        Write-Host "❌ PostgreSQL psql command not found." -ForegroundColor Red
        Write-Host "Please install PostgreSQL client tools or add them to PATH." -ForegroundColor Yellow
        exit 1
    }

    # Execute the SQL script
    Write-Host "📊 Executing SQL reset script..." -ForegroundColor Yellow
    
    $env:PGPASSWORD = "postgres"
    $result = psql -h localhost -U postgres -d postgres -f reset_graph_tables.sql 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Database reset completed successfully!" -ForegroundColor Green
        Write-Host $result -ForegroundColor Gray
    } else {
        Write-Host "❌ Database reset failed:" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "❌ Error during database reset: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n" + "=" * 60 -ForegroundColor Yellow
Write-Host "🎉 Reset completed successfully!" -ForegroundColor Green
Write-Host "📝 Next steps:" -ForegroundColor Cyan
Write-Host "  1. Set environment variable: `$env:R2R_EMBEDDING_DIMENSION='1024'" -ForegroundColor White
Write-Host "  2. Start R2R server: uv run r2r-serve --full" -ForegroundColor White
Write-Host "  3. The system will recreate tables with 1024 dimensions" -ForegroundColor White
Write-Host "=" * 60 -ForegroundColor Yellow
