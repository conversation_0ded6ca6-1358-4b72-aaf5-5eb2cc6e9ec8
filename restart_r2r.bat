@echo off
chcp 65001 >nul
echo ==========================================
echo R2R服务重启脚本
echo ==========================================
echo.

echo 🛑 停止现有R2R服务...

:: 查找并停止R2R相关进程
tasklist /FI "IMAGENAME eq python.exe" | findstr "python.exe" >nul 2>&1
if %errorlevel% equ 0 (
    echo 🔍 发现Python进程，尝试停止R2R服务...
    
    :: 尝试优雅停止
    for /f "tokens=2" %%a in ('tasklist /FI "IMAGENAME eq python.exe" /FO CSV ^| findstr "python.exe"') do (
        set "pid=%%a"
        set "pid=!pid:"=!"
        taskkill /PID !pid! >nul 2>&1
    )
    
    timeout /t 3 >nul
)

:: 检查端口占用并强制释放
netstat -ano | findstr ":7272" >nul 2>&1
if %errorlevel% equ 0 (
    echo 🔍 端口7272仍被占用，强制释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":7272"') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 2 >nul
)

echo ✅ 服务已停止

echo.
echo 🚀 启动R2R服务...

:: 切换到正确的目录
if exist "backend\py" (
    cd backend\py
    echo 📁 切换到目录: backend\py
) else if exist "py" (
    cd py
    echo 📁 切换到目录: py
) else (
    echo ❌ 未找到Python目录
    pause
    exit /b 1
)

:: 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo 🐍 激活虚拟环境...
    call .venv\Scripts\activate.bat
)

:: 启动服务
echo 🌐 启动R2R服务器...
echo 📊 配置的向量维度: 1024
echo 🔧 修改后的代码将自动重建不匹配的表
echo.

start "R2R Server" cmd /k "python -m r2r serve --host 0.0.0.0 --port 7272"

:: 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 15 >nul

:: 检查服务状态
echo 🔍 检查服务状态...
curl -s http://localhost:7272/v3/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ R2R服务启动成功!
    echo 🌐 服务地址: http://localhost:7272
    echo 📋 健康检查: http://localhost:7272/v3/health
) else (
    echo ⚠️  服务可能还在启动中...
    echo 🌐 请稍后访问: http://localhost:7272
    echo 📋 或检查服务窗口中的启动日志
)

echo.
echo ==========================================
echo ✅ 重启完成!
echo ==========================================
echo.
echo 📝 接下来:
echo 1. 访问 http://localhost:7272 验证服务
echo 2. 尝试重新执行图RAG操作
echo 3. 检查日志确认表已重建为1024维度
echo.

pause
