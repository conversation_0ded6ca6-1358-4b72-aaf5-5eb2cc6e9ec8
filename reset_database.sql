-- 完全重置数据库脚本
-- 删除所有向量相关表，准备重新初始化为1024维度

\echo '=========================================='
\echo '开始重置数据库维度 - 目标维度: 1024'
\echo '=========================================='

-- 连接到数据库
\c postgres

-- 检查当前schema状态
\echo ''
\echo '检查当前schema状态...'
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'cscsrag'
ORDER BY tablename;

-- 检查向量字段维度
\echo ''
\echo '检查当前向量字段维度...'
SELECT 
    t.table_name,
    c.column_name,
    pg_catalog.format_type(a.atttypid, a.atttypmod) as vector_type
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name
JOIN pg_attribute a ON a.attname = c.column_name
JOIN pg_class cl ON a.attrelid = cl.oid
JOIN pg_namespace n ON cl.relnamespace = n.oid
WHERE t.table_schema = 'cscsrag'
AND n.nspname = 'cscsrag'
AND cl.relname = t.table_name
AND (pg_catalog.format_type(a.atttypid, a.atttypmod) LIKE '%vector%'
     OR c.column_name LIKE '%embedding%')
ORDER BY t.table_name, c.column_name;

\echo ''
\echo '⚠️  警告: 即将删除schema cscsrag 及其所有数据!'
\echo '按 Ctrl+C 取消，或按回车继续...'
\prompt '确认继续删除? (输入 YES): ' confirm

-- 如果用户确认，继续执行删除
\echo ''
\echo '开始删除所有表...'

-- 删除所有表（按依赖关系顺序）
DROP TABLE IF EXISTS "cscsrag"."conversations" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."tokens" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."prompts" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."limits" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."file_processing_logs" CASCADE;

-- 删除图相关表
DROP TABLE IF EXISTS "cscsrag"."graphs_communities" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."graphs_relationships" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."documents_relationships" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."graphs_entities" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."documents_entities" CASCADE;

-- 删除核心表
DROP TABLE IF EXISTS "cscsrag"."chunks" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."documents" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."collections" CASCADE;
DROP TABLE IF EXISTS "cscsrag"."users" CASCADE;

\echo '✅ 所有表已删除'

-- 删除并重建schema
\echo ''
\echo '重建schema...'
DROP SCHEMA IF EXISTS "cscsrag" CASCADE;
CREATE SCHEMA IF NOT EXISTS "cscsrag";

\echo '✅ Schema已重建'

-- 确保必要的扩展已安装
\echo ''
\echo '检查数据库扩展...'
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS vector;
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;

\echo '✅ 扩展已启用'

-- 验证vector扩展
\echo ''
\echo '验证vector扩展...'
SELECT extname, extversion FROM pg_extension WHERE extname = 'vector';

-- 测试1024维向量创建
\echo ''
\echo '测试1024维向量支持...'
DO $$
DECLARE
    test_vector text;
BEGIN
    -- 创建一个1024维的测试向量
    SELECT array_to_string(array_agg(random()::text), ',') 
    INTO test_vector
    FROM generate_series(1, 1024);
    
    -- 测试创建vector(1024)类型
    EXECUTE format('SELECT (''[%s]'')::vector(1024)', test_vector);
    
    RAISE NOTICE '✅ 1024维向量支持正常';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE '❌ 1024维向量测试失败: %', SQLERRM;
END
$$;

-- 显示最终状态
\echo ''
\echo '=========================================='
\echo '数据库重置完成!'
\echo '=========================================='
\echo ''
\echo '📝 接下来的步骤:'
\echo '1. 重启R2R服务'
\echo '2. 服务会自动创建新的1024维度表'
\echo '3. 重新导入数据'
\echo ''

-- 显示当前schema状态（应该为空）
\echo '当前schema状态:'
SELECT 
    schemaname,
    tablename
FROM pg_tables 
WHERE schemaname = 'cscsrag'
ORDER BY tablename;

\echo ''
\echo '如果上面没有显示任何表，说明重置成功!'
