#!/usr/bin/env python3
"""
AnythingChat 配置生成工具
根据用户输入生成相应的配置文件
"""

import os
import sys
from typing import Dict, Any, Optional
import json

def get_user_input(prompt: str, default: str = "", required: bool = False) -> str:
    """获取用户输入"""
    if default:
        full_prompt = f"{prompt} [{default}]: "
    else:
        full_prompt = f"{prompt}: "
    
    while True:
        value = input(full_prompt).strip()
        if not value and default:
            return default
        elif not value and required:
            print("❌ 此项为必填项，请输入值")
            continue
        else:
            return value

def get_yes_no(prompt: str, default: bool = False) -> bool:
    """获取是/否输入"""
    default_str = "Y/n" if default else "y/N"
    while True:
        value = input(f"{prompt} [{default_str}]: ").strip().lower()
        if not value:
            return default
        elif value in ['y', 'yes', '是']:
            return True
        elif value in ['n', 'no', '否']:
            return False
        else:
            print("请输入 y/yes/是 或 n/no/否")

def collect_basic_config() -> Dict[str, Any]:
    """收集基本配置信息"""
    print("📋 基本配置")
    print("-" * 30)
    
    config = {}
    config['project_name'] = get_user_input(
        "项目名称", 
        "anythingchat", 
        required=True
    )
    
    config['environment'] = get_user_input(
        "环境类型 (development/production)", 
        "development"
    )
    
    return config

def collect_database_config() -> Dict[str, Any]:
    """收集数据库配置"""
    print("\n🗄️ 数据库配置")
    print("-" * 30)
    
    config = {}
    config['host'] = get_user_input("数据库主机", "localhost", required=True)
    config['port'] = get_user_input("数据库端口", "5432", required=True)
    config['user'] = get_user_input("数据库用户名", "postgres", required=True)
    config['password'] = get_user_input("数据库密码", required=True)
    config['database'] = get_user_input("数据库名称", "r2r", required=True)
    
    # 高级配置
    if get_yes_no("配置数据库性能参数"):
        config['max_connections'] = get_user_input("最大连接数", "256")
        config['statement_cache_size'] = get_user_input("语句缓存大小", "100")
    
    return config

def collect_storage_config() -> Dict[str, Any]:
    """收集存储配置"""
    print("\n🗂️ 文件存储配置")
    print("-" * 30)
    
    storage_type = get_user_input(
        "存储类型 (postgres/s3/minio)", 
        "postgres"
    ).lower()
    
    config = {'provider': storage_type}
    
    if storage_type in ['s3', 'minio']:
        config['bucket_name'] = get_user_input("存储桶名称", required=True)
        config['access_key'] = get_user_input("访问密钥", required=True)
        config['secret_key'] = get_user_input("秘密密钥", required=True)
        config['region'] = get_user_input("区域", "us-east-1")
        
        if storage_type == 'minio':
            config['endpoint_url'] = get_user_input(
                "MinIO服务器地址", 
                "http://localhost:9000", 
                required=True
            )
    
    return config

def collect_llm_config() -> Dict[str, Any]:
    """收集LLM配置"""
    print("\n🤖 大模型配置")
    print("-" * 30)
    
    config = {}
    
    # 选择主要提供商
    print("可用的LLM提供商:")
    print("1. OpenAI")
    print("2. Anthropic")
    print("3. Azure OpenAI")
    print("4. Google Gemini")
    print("5. Ollama (本地)")
    print("6. 其他")
    
    provider_choice = get_user_input("选择主要提供商 (1-6)", "1")
    
    provider_map = {
        '1': 'openai',
        '2': 'anthropic', 
        '3': 'azure',
        '4': 'google',
        '5': 'ollama',
        '6': 'other'
    }
    
    provider = provider_map.get(provider_choice, 'openai')
    config['primary_provider'] = provider
    
    # 根据提供商收集配置
    if provider == 'openai':
        config['openai_api_key'] = get_user_input("OpenAI API密钥", required=True)
        config['fast_llm'] = get_user_input("快速模型", "openai/gpt-4.1-mini")
        config['quality_llm'] = get_user_input("高质量模型", "openai/gpt-4.1")
        config['embedding_model'] = get_user_input("嵌入模型", "openai/text-embedding-3-small")
        
    elif provider == 'anthropic':
        config['anthropic_api_key'] = get_user_input("Anthropic API密钥", required=True)
        config['fast_llm'] = get_user_input("快速模型", "anthropic/claude-3-haiku")
        config['quality_llm'] = get_user_input("高质量模型", "anthropic/claude-3-sonnet")
        
    elif provider == 'azure':
        config['azure_api_key'] = get_user_input("Azure API密钥", required=True)
        config['azure_api_base'] = get_user_input("Azure API基础URL", required=True)
        config['azure_api_version'] = get_user_input("Azure API版本", "2024-02-15-preview")
        config['fast_llm'] = get_user_input("快速模型", "azure/gpt-4.1-mini")
        config['quality_llm'] = get_user_input("高质量模型", "azure/gpt-4.1")
        
    elif provider == 'ollama':
        config['ollama_api_base'] = get_user_input("Ollama API地址", "http://localhost:11434")
        config['fast_llm'] = get_user_input("快速模型", "ollama/llama3.1")
        config['quality_llm'] = get_user_input("高质量模型", "ollama/llama3.1")
    
    # 通用配置
    config['concurrent_limit'] = get_user_input("并发请求限制", "64")
    config['request_timeout'] = get_user_input("请求超时时间(秒)", "60")
    
    return config

def collect_auth_config() -> Dict[str, Any]:
    """收集认证配置"""
    print("\n🔐 认证配置")
    print("-" * 30)
    
    config = {}
    config['require_authentication'] = get_yes_no("启用用户认证", False)
    
    if config['require_authentication']:
        config['require_email_verification'] = get_yes_no("启用邮箱验证", False)
        config['admin_email'] = get_user_input("管理员邮箱", "<EMAIL>")
        config['admin_password'] = get_user_input("管理员密码", "change_me_immediately")
        config['access_token_lifetime'] = get_user_input("访问令牌生命周期(分钟)", "60")
        config['refresh_token_lifetime'] = get_user_input("刷新令牌生命周期(天)", "7")
    
    return config

def generate_toml_config(config_data: Dict[str, Any]) -> str:
    """生成TOML配置文件内容"""
    toml_content = f"""# AnythingChat 配置文件
# 项目: {config_data['basic']['project_name']}
# 环境: {config_data['basic']['environment']}
# 生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

[app]
project_name = "{config_data['basic']['project_name']}"
"""
    
    # LLM配置
    llm_config = config_data['llm']
    toml_content += f"""fast_llm = "{llm_config.get('fast_llm', 'openai/gpt-4.1-mini')}"
quality_llm = "{llm_config.get('quality_llm', 'openai/gpt-4.1')}"
vlm = "{llm_config.get('quality_llm', 'openai/gpt-4.1')}"
audio_lm = "openai/whisper-1"

"""
    
    # 认证配置
    auth_config = config_data['auth']
    toml_content += f"""[auth]
provider = "r2r"
require_authentication = {str(auth_config.get('require_authentication', False)).lower()}
require_email_verification = {str(auth_config.get('require_email_verification', False)).lower()}
"""
    
    if auth_config.get('require_authentication'):
        toml_content += f"""default_admin_email = "{auth_config.get('admin_email', '<EMAIL>')}"
default_admin_password = "{auth_config.get('admin_password', 'change_me_immediately')}"
access_token_lifetime_in_minutes = {auth_config.get('access_token_lifetime', '60')}
refresh_token_lifetime_in_days = {auth_config.get('refresh_token_lifetime', '7')}
"""
    
    # 完成配置
    toml_content += f"""
[completion]
provider = "r2r"
concurrent_request_limit = {llm_config.get('concurrent_limit', '64')}
request_timeout = {llm_config.get('request_timeout', '60')}

[completion.generation_config]
temperature = 0.1
top_p = 1
max_tokens_to_sample = 4_096
stream = false

"""
    
    # 嵌入配置
    embedding_model = llm_config.get('embedding_model', 'openai/text-embedding-3-small')
    toml_content += f"""[embedding]
provider = "litellm"
base_model = "{embedding_model}"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256

[completion_embedding]
provider = "litellm"
base_model = "{embedding_model}"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256

"""
    
    # 数据库配置
    toml_content += """[database]
provider = "postgres"
default_collection_name = "Default"
default_collection_description = "Your default collection."

"""
    
    # 文件存储配置
    storage_config = config_data['storage']
    toml_content += f"""[file]
provider = "{storage_config['provider']}"

"""
    
    # 其他配置
    toml_content += """[ingestion]
provider = "r2r"
chunking_strategy = "recursive"
chunk_size = 1_024
chunk_overlap = 512
automatic_extraction = true

[orchestration]
provider = "simple"

[crypto]
provider = "bcrypt"

[email]
provider = "console_mock"

[scheduler]
provider = "apscheduler"
"""
    
    return toml_content

def generate_env_file(config_data: Dict[str, Any]) -> str:
    """生成环境变量文件内容"""
    env_content = f"""# AnythingChat 环境变量配置
# 项目: {config_data['basic']['project_name']}
# 环境: {config_data['basic']['environment']}

# 系统配置
R2R_PROJECT_NAME={config_data['basic']['project_name']}
R2R_HOST=0.0.0.0
R2R_PORT=7272

# 数据库配置
"""
    
    db_config = config_data['database']
    env_content += f"""R2R_POSTGRES_USER={db_config['user']}
R2R_POSTGRES_PASSWORD={db_config['password']}
R2R_POSTGRES_HOST={db_config['host']}
R2R_POSTGRES_PORT={db_config['port']}
R2R_POSTGRES_DBNAME={db_config['database']}
"""
    
    if db_config.get('max_connections'):
        env_content += f"R2R_POSTGRES_MAX_CONNECTIONS={db_config['max_connections']}\n"
    if db_config.get('statement_cache_size'):
        env_content += f"R2R_POSTGRES_STATEMENT_CACHE_SIZE={db_config['statement_cache_size']}\n"
    
    # LLM API配置
    llm_config = config_data['llm']
    env_content += "\n# LLM API配置\n"
    
    if llm_config.get('openai_api_key'):
        env_content += f"OPENAI_API_KEY={llm_config['openai_api_key']}\n"
    if llm_config.get('anthropic_api_key'):
        env_content += f"ANTHROPIC_API_KEY={llm_config['anthropic_api_key']}\n"
    if llm_config.get('azure_api_key'):
        env_content += f"AZURE_API_KEY={llm_config['azure_api_key']}\n"
        env_content += f"AZURE_API_BASE={llm_config['azure_api_base']}\n"
        env_content += f"AZURE_API_VERSION={llm_config['azure_api_version']}\n"
    if llm_config.get('ollama_api_base'):
        env_content += f"OLLAMA_API_BASE={llm_config['ollama_api_base']}\n"
    
    # 存储配置
    storage_config = config_data['storage']
    if storage_config['provider'] in ['s3', 'minio']:
        env_content += "\n# 存储配置\n"
        env_content += f"S3_BUCKET_NAME={storage_config['bucket_name']}\n"
        env_content += f"AWS_ACCESS_KEY_ID={storage_config['access_key']}\n"
        env_content += f"AWS_SECRET_ACCESS_KEY={storage_config['secret_key']}\n"
        env_content += f"AWS_REGION={storage_config['region']}\n"
        if storage_config.get('endpoint_url'):
            env_content += f"S3_ENDPOINT_URL={storage_config['endpoint_url']}\n"
    
    return env_content

def main():
    """主函数"""
    print("🚀 AnythingChat 配置生成工具")
    print("=" * 50)
    print("此工具将帮助您生成 AnythingChat 的配置文件")
    print()
    
    # 收集配置信息
    config_data = {}
    config_data['basic'] = collect_basic_config()
    config_data['database'] = collect_database_config()
    config_data['storage'] = collect_storage_config()
    config_data['llm'] = collect_llm_config()
    config_data['auth'] = collect_auth_config()
    
    # 生成配置文件
    print("\n📝 生成配置文件...")
    
    # 生成TOML配置
    toml_content = generate_toml_config(config_data)
    toml_filename = f"{config_data['basic']['project_name']}.toml"
    
    with open(toml_filename, 'w', encoding='utf-8') as f:
        f.write(toml_content)
    
    # 生成环境变量文件
    env_content = generate_env_file(config_data)
    env_filename = f"{config_data['basic']['project_name']}.env"
    
    with open(env_filename, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    # 保存配置数据
    config_filename = f"{config_data['basic']['project_name']}_config.json"
    with open(config_filename, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, indent=2, ensure_ascii=False)
    
    print("✅ 配置文件生成完成！")
    print(f"📄 TOML配置文件: {toml_filename}")
    print(f"📄 环境变量文件: {env_filename}")
    print(f"📄 配置数据文件: {config_filename}")
    
    print("\n📋 下一步操作:")
    print(f"1. 将 {toml_filename} 复制到 backend/py/core/configs/ 目录")
    print(f"2. 设置环境变量: export R2R_CONFIG_NAME={config_data['basic']['project_name']}")
    print(f"3. 或者加载环境变量文件: source {env_filename}")
    print("4. 启动 AnythingChat 服务")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ 配置生成已取消")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 生成配置时出现错误: {e}")
        sys.exit(1)
