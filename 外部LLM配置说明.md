# 外部LLM配置说明

## 配置概述

已成功配置外部大模型API到R2R系统中，具体信息如下：

- **API地址**: `http://***********:3000/v1`
- **模型名称**: `volcengine/deepseek-v3`
- **API密钥**: `sk-onCliIrxDPaFG2tXZ3zhpXrPu5MrqZeWujnswtjeN7AsiKz8`

## 配置文件修改

### 1. 环境变量配置 (`backend/.env`)

```bash
# Environment variables for LLM provider(s)
export OPENAI_API_KEY=sk-onCliIrxDPaFG2tXZ3zhpXrPu5MrqZeWujnswtjeN7AsiKz8
export OPENAI_BASE_URL=http://***********:3000/v1
#  uncomment the following lines to enable other LLM providers
# export ANTHROPIC_API_KEY=...
# export VERTEX_API_KEY=...
# export XAI_API_KEY=...
# Add other provider keys as needed

# Custom External LLM Configuration (backup)
export VOLCENGINE_API_KEY=sk-onCliIrxDPaFG2tXZ3zhpXrPu5MrqZeWujnswtjeN7AsiKz8
export VOLCENGINE_API_BASE=http://***********:3000/v1
```

### 2. R2R配置文件 (`backend/py/r2r/r2r.toml`)

```toml
[app]
# LLM used for internal operations, like deriving conversation names
fast_llm = "openai/volcengine/deepseek-v3"

# LLM used for user-facing output, like RAG replies
quality_llm = "openai/volcengine/deepseek-v3"

# LLM used for ingesting visual inputs
vlm = "openai/volcengine/deepseek-v3"

# LLM used for transcription
audio_lm = "openai/whisper-1"

# Reasoning model, used for `research` agent
reasoning_llm = "openai/volcengine/deepseek-v3"
# Planning model, used for `research` agent
planning_llm = "openai/volcengine/deepseek-v3"

[completion]
provider = "r2r"
concurrent_request_limit = 64
request_timeout = 60

  [completion.generation_config]
  temperature = 0.1
  top_p = 1
  max_tokens_to_sample = 4_096
  stream = false
  add_generation_kwargs = { }
```

## 配置原理

### 1. 使用OpenAI兼容接口

由于外部API使用OpenAI兼容格式，我们采用以下策略：

1. **环境变量配置**: 设置`OPENAI_API_KEY`和`OPENAI_BASE_URL`
2. **模型前缀**: 使用`openai/`前缀让R2R路由到OpenAI provider
3. **完整模型名**: 使用`openai/volcengine/deepseek-v3`作为完整模型标识

### 2. R2R路由机制

R2R的`R2RCompletionProvider`会根据模型前缀自动路由：
- `openai/` → `OpenAICompletionProvider`
- `anthropic/` → `AnthropicCompletionProvider`
- `litellm/` → `LiteLLMCompletionProvider`

### 3. OpenAI客户端配置

OpenAI客户端会自动读取环境变量：
- `OPENAI_API_KEY` → API密钥
- `OPENAI_BASE_URL` → 自定义API基础URL

## 测试结果

### ✅ 成功的测试

1. **同步OpenAI客户端**: 能够正确连接外部API并获得响应
2. **异步OpenAI客户端**: 异步调用工作正常
3. **模型响应**: 返回`deepseek-v3-250324`，确认API调用成功

### 测试输出示例

```
=== 测试同步OpenAI客户端 ===
客户端base_url: http://***********:3000/v1/
响应成功!
模型: deepseek-v3-250324
内容: 你好！我是 **DeepSeek Chat**，由深度求索公司（DeepSeek）研发的智能 AI 助手...
```

## 启动R2R服务

要使用配置好的外部LLM，请按以下步骤启动R2R服务：

```bash
# 进入backend目录
cd backend

# 启动R2R服务
python -m r2r serve --config-name=r2r
```

## 验证配置

可以使用提供的测试脚本验证配置：

```bash
# 测试OpenAI客户端连接
python test_external_llm.py

# 测试R2R服务（需要先启动服务）
python test_r2r_simple.py
```

## 注意事项

1. **模型名称**: 在API调用中使用`volcengine/deepseek-v3`
2. **网络连接**: 确保能够访问`http://***********:3000/v1`
3. **API密钥**: 确保密钥有效且有足够的配额
4. **依赖项**: 如果遇到依赖问题，可能需要安装额外的Python包

## 故障排除

### 常见问题

1. **连接失败**: 检查网络连接和API地址
2. **认证失败**: 验证API密钥是否正确
3. **模型不可用**: 确认模型名称和可用性
4. **依赖缺失**: 安装所需的Python包

### 日志查看

启动R2R服务时，可以查看日志了解详细的错误信息：

```bash
python -m r2r serve --config-name=r2r --log-level=DEBUG
```

## 总结

✅ **配置完成**: 外部LLM已成功配置到R2R系统中
✅ **测试通过**: OpenAI客户端能够正常连接和调用外部API
✅ **配置文件**: 所有必要的配置文件已正确修改
✅ **文档完整**: 提供了完整的配置说明和测试方法

现在可以启动R2R服务并使用配置好的外部大模型进行RAG问答了！
