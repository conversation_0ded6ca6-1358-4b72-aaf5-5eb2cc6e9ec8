# Set environment variables for R2R with correct dimensions
Write-Host "Setting R2R environment variables..." -ForegroundColor Green

# Set embedding dimension to 512 to match existing database
$env:R2R_EMBEDDING_DIMENSION = "512"
$env:R2R_PROJECT_NAME = "cscsrag"

# Database settings
$env:R2R_POSTGRES_USER = "postgres"
$env:R2R_POSTGRES_PASSWORD = "postgres"
$env:R2R_POSTGRES_HOST = "localhost"
$env:R2R_POSTGRES_PORT = "5432"
$env:R2R_POSTGRES_DBNAME = "postgres"

Write-Host "Environment variables set:" -ForegroundColor Yellow
Write-Host "  R2R_EMBEDDING_DIMENSION = $env:R2R_EMBEDDING_DIMENSION" -ForegroundColor Cyan
Write-Host "  R2R_PROJECT_NAME = $env:R2R_PROJECT_NAME" -ForegroundColor Cyan
Write-Host "  Database connection configured" -ForegroundColor Cyan

Write-Host "`nY<PERSON> can now start the R2R server with:" -ForegroundColor Green
Write-Host "  uv run r2r-serve --full" -ForegroundColor White
