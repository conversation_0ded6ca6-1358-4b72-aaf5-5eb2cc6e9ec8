@echo off
chcp 65001 >nul
echo ==========================================
echo 数据库维度重置工具
echo ==========================================
echo.

echo 🔍 检查PostgreSQL连接...

:: 尝试不同的PostgreSQL连接方式
set PGPASSWORD=postgres

:: 方式1: 尝试默认连接
psql -h localhost -U postgres -d postgres -c "SELECT version();" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL连接成功
    goto :execute_reset
)

:: 方式2: 尝试其他常见端口
psql -h localhost -p 5433 -U postgres -d postgres -c "SELECT version();" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL连接成功 (端口5433)
    set PGPORT=5433
    goto :execute_reset
)

:: 方式3: 尝试本地连接
psql -U postgres -d postgres -c "SELECT version();" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ PostgreSQL连接成功 (本地)
    goto :execute_reset
)

echo ❌ 无法连接到PostgreSQL数据库
echo.
echo 请检查:
echo 1. PostgreSQL服务是否运行
echo 2. 用户名密码是否正确 (当前使用: postgres/postgres)
echo 3. 端口是否正确 (尝试了5432和5433)
echo.
pause
exit /b 1

:execute_reset
echo.
echo ⚠️  警告: 即将删除所有数据库表和数据!
echo 这个操作不可逆转!
echo.
set /p confirm="确认继续? (输入 YES 继续): "

if not "%confirm%"=="YES" (
    echo ❌ 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始执行数据库重置...
echo.

:: 执行SQL重置脚本
if defined PGPORT (
    psql -h localhost -p %PGPORT% -U postgres -d postgres -f reset_database.sql
) else (
    psql -h localhost -U postgres -d postgres -f reset_database.sql
)

if %errorlevel% equ 0 (
    echo.
    echo ✅ 数据库重置完成!
    echo.
    echo 📝 接下来需要:
    echo 1. 重启R2R服务
    echo 2. 服务会自动创建新的1024维度表
    echo 3. 重新导入数据
) else (
    echo.
    echo ❌ 数据库重置过程中出现错误
    echo 请检查上面的错误信息
)

echo.
pause
