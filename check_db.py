import asyncio
import asyncpg
import os

async def check_tables():
    try:
        conn = await asyncpg.connect('postgresql://postgres:postgres@localhost:5432/postgres')
        
        # Check graph tables with embedding columns
        result = await conn.fetch("""
            SELECT table_name, column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'cscsrag' 
            AND table_name LIKE '%graph%' 
            AND column_name LIKE '%embedding%'
        """)
        
        print('Graph tables with embedding columns:')
        for row in result:
            print(f'Table: {row["table_name"]}, Column: {row["column_name"]}, Type: {row["data_type"]}')
        
        # Check vector dimensions
        vector_result = await conn.fetch("""
            SELECT 
                t.table_name,
                c.column_name,
                c.data_type,
                CASE 
                    WHEN c.data_type = 'USER-DEFINED' THEN 
                        (SELECT pg_catalog.format_type(a.atttypid, a.atttypmod) 
                         FROM pg_attribute a 
                         JOIN pg_class cl ON a.attrelid = cl.oid 
                         JOIN pg_namespace n ON cl.relnamespace = n.oid
                         WHERE n.nspname = 'cscsrag' 
                         AND cl.relname = t.table_name 
                         AND a.attname = c.column_name)
                    ELSE c.data_type 
                END as full_type
            FROM information_schema.tables t
            JOIN information_schema.columns c ON t.table_name = c.table_name
            WHERE t.table_schema = 'cscsrag' 
            AND t.table_name LIKE '%graph%' 
            AND c.column_name LIKE '%embedding%'
        """)
        
        print('\nDetailed vector information:')
        for row in vector_result:
            print(f'Table: {row["table_name"]}, Column: {row["column_name"]}, Full Type: {row["full_type"]}')
            
        await conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_tables())
