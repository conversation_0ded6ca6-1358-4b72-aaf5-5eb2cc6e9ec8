#!/usr/bin/env python3
"""
完全重置数据库维度脚本
删除所有向量相关表，重新初始化为1024维度
"""

import asyncio
import asyncpg
import os
import sys
from typing import List

# 数据库连接配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'user': 'postgres',
    'password': 'postgres',
    'database': 'postgres'
}

# 项目schema名称
SCHEMA_NAME = 'cscsrag'

# 需要删除的表列表（包含向量字段的表）
VECTOR_TABLES = [
    'chunks',
    'documents',
    'documents_entities',
    'graphs_entities', 
    'documents_relationships',
    'graphs_relationships',
    'graphs_communities',
    'collections'
]

# 其他可能的相关表
OTHER_TABLES = [
    'conversations',
    'users',
    'tokens',
    'prompts',
    'limits',
    'file_processing_logs'
]

async def connect_db():
    """连接数据库"""
    try:
        conn = await asyncpg.connect(**DB_CONFIG)
        print("✅ 数据库连接成功")
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

async def check_schema_exists(conn: asyncpg.Connection) -> bool:
    """检查schema是否存在"""
    result = await conn.fetchval(
        "SELECT EXISTS(SELECT 1 FROM information_schema.schemata WHERE schema_name = $1)",
        SCHEMA_NAME
    )
    return result

async def list_tables_in_schema(conn: asyncpg.Connection) -> List[str]:
    """列出schema中的所有表"""
    if not await check_schema_exists(conn):
        return []
    
    tables = await conn.fetch(
        "SELECT table_name FROM information_schema.tables WHERE table_schema = $1",
        SCHEMA_NAME
    )
    return [table['table_name'] for table in tables]

async def drop_all_tables(conn: asyncpg.Connection):
    """删除schema中的所有表"""
    if not await check_schema_exists(conn):
        print(f"⚠️  Schema '{SCHEMA_NAME}' 不存在")
        return
    
    tables = await list_tables_in_schema(conn)
    if not tables:
        print(f"⚠️  Schema '{SCHEMA_NAME}' 中没有表")
        return
    
    print(f"🗑️  准备删除 {len(tables)} 个表...")
    
    # 删除所有表（CASCADE删除依赖）
    for table in tables:
        try:
            await conn.execute(f'DROP TABLE IF EXISTS "{SCHEMA_NAME}"."{table}" CASCADE')
            print(f"   ✅ 删除表: {table}")
        except Exception as e:
            print(f"   ❌ 删除表 {table} 失败: {e}")

async def drop_schema(conn: asyncpg.Connection):
    """删除整个schema"""
    try:
        await conn.execute(f'DROP SCHEMA IF EXISTS "{SCHEMA_NAME}" CASCADE')
        print(f"✅ 删除schema: {SCHEMA_NAME}")
    except Exception as e:
        print(f"❌ 删除schema失败: {e}")

async def create_schema(conn: asyncpg.Connection):
    """创建schema"""
    try:
        await conn.execute(f'CREATE SCHEMA IF NOT EXISTS "{SCHEMA_NAME}"')
        print(f"✅ 创建schema: {SCHEMA_NAME}")
    except Exception as e:
        print(f"❌ 创建schema失败: {e}")

async def ensure_extensions(conn: asyncpg.Connection):
    """确保必要的扩展已安装"""
    extensions = [
        'uuid-ossp',
        'vector',
        'pg_trgm',
        'fuzzystrmatch'
    ]
    
    for ext in extensions:
        try:
            await conn.execute(f'CREATE EXTENSION IF NOT EXISTS "{ext}"')
            print(f"✅ 扩展已启用: {ext}")
        except Exception as e:
            print(f"❌ 启用扩展 {ext} 失败: {e}")

async def verify_vector_extension(conn: asyncpg.Connection):
    """验证vector扩展和版本"""
    try:
        result = await conn.fetchval("SELECT extversion FROM pg_extension WHERE extname = 'vector'")
        if result:
            print(f"✅ pgvector版本: {result}")
            
            # 测试创建1024维向量
            await conn.execute("SELECT '[1,2,3]'::vector(3)")
            print("✅ vector扩展工作正常")
        else:
            print("❌ vector扩展未安装")
    except Exception as e:
        print(f"❌ vector扩展验证失败: {e}")

async def main():
    """主函数"""
    print("🚀 开始重置数据库维度...")
    print(f"📊 目标维度: 1024")
    print(f"🎯 目标schema: {SCHEMA_NAME}")
    print("=" * 50)
    
    # 连接数据库
    conn = await connect_db()
    
    try:
        # 1. 检查当前状态
        print("\n📋 检查当前状态...")
        tables = await list_tables_in_schema(conn)
        print(f"   当前表数量: {len(tables)}")
        if tables:
            print(f"   表列表: {', '.join(tables)}")
        
        # 2. 确认删除
        print(f"\n⚠️  警告: 即将删除schema '{SCHEMA_NAME}' 及其所有数据!")
        confirm = input("确认继续? (输入 'YES' 继续): ")
        if confirm != 'YES':
            print("❌ 操作已取消")
            return
        
        # 3. 删除所有表
        print(f"\n🗑️  删除所有表...")
        await drop_all_tables(conn)
        
        # 4. 删除并重建schema
        print(f"\n🔄 重建schema...")
        await drop_schema(conn)
        await create_schema(conn)
        
        # 5. 确保扩展
        print(f"\n🔧 检查数据库扩展...")
        await ensure_extensions(conn)
        await verify_vector_extension(conn)
        
        print("\n" + "=" * 50)
        print("✅ 数据库重置完成!")
        print("📝 接下来需要:")
        print("   1. 重启R2R服务")
        print("   2. 服务会自动创建新的1024维度表")
        print("   3. 重新导入数据")
        
    except Exception as e:
        print(f"\n❌ 重置过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        await conn.close()
        print("\n🔌 数据库连接已关闭")

if __name__ == "__main__":
    asyncio.run(main())
