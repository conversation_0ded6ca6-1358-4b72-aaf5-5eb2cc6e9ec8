#!/usr/bin/env python3
"""
测试embedding修复的脚本
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加backend路径到sys.path
backend_path = Path(__file__).parent / "backend" / "py"
sys.path.insert(0, str(backend_path))

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["OPENAI_BASE_URL"] = "http://172.17.9.46:3000/v1"
os.environ["ALIYUN_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["ALIYUN_API_BASE"] = "http://172.17.9.46:3000/v1"

print("🔧 测试embedding修复...")
print(f"📍 Backend路径: {backend_path}")
print(f"🔑 API密钥: {os.environ['OPENAI_API_KEY'][:20]}...")
print(f"🌐 API地址: {os.environ['OPENAI_BASE_URL']}")

try:
    # 导入必要的模块
    from core.base.providers.embedding import EmbeddingConfig
    from core.providers.embeddings.litellm import LiteLLMEmbeddingProvider
    
    print("✅ 成功导入R2R模块")
    
    # 测试配置创建
    config_data = {
        "provider": "litellm",
        "base_model": "openai/aliyun/text-embedding-v4",
        "base_dimension": 512,
        "batch_size": 128,
        "concurrent_request_limit": 256,
        "api_base": "http://172.17.9.46:3000/v1"
    }
    
    print(f"📋 创建配置: {config_data}")
    
    # 创建配置对象
    config = EmbeddingConfig.create(**config_data)
    print(f"✅ 成功创建EmbeddingConfig")
    print(f"   - provider: {config.provider}")
    print(f"   - base_model: {config.base_model}")
    print(f"   - base_dimension: {config.base_dimension}")
    print(f"   - extra_fields: {getattr(config, 'extra_fields', {})}")
    
    # 创建provider
    provider = LiteLLMEmbeddingProvider(config)
    print(f"✅ 成功创建LiteLLMEmbeddingProvider")
    
    # 测试_get_embedding_kwargs方法
    kwargs = provider._get_embedding_kwargs()
    print(f"📋 生成的embedding kwargs:")
    for key, value in kwargs.items():
        if key == 'api_key' and value:
            print(f"   - {key}: {value[:20]}...")
        else:
            print(f"   - {key}: {value}")
    
    # 检查关键参数
    if 'api_base' in kwargs:
        print("✅ api_base 正确传递")
    else:
        print("❌ api_base 未传递")
        
    if 'api_key' in kwargs:
        print("✅ api_key 正确传递")
    else:
        print("❌ api_key 未传递")
    
    if kwargs.get('model', '').startswith('openai/'):
        print("✅ 模型名称格式正确")
    else:
        print("❌ 模型名称格式可能有问题")
    
    print("\n🎯 配置验证完成！")
    print("💡 建议：")
    print("   1. 确保new-api服务在 http://172.17.9.46:3000 正常运行")
    print("   2. 确认模型 'aliyun/text-embedding-v4' 在new-api中正确配置")
    print("   3. 重启R2R服务以应用配置更改")
    
except ImportError as e:
    print(f"❌ 导入模块失败: {str(e)}")
    print("💡 请确保在正确的Python环境中运行此脚本")
    print("   建议使用: uv run python test_embedding_fix.py")
except Exception as e:
    print(f"❌ 测试过程中出现错误: {str(e)}")
    print(f"   - 错误类型: {type(e).__name__}")
    import traceback
    traceback.print_exc()
