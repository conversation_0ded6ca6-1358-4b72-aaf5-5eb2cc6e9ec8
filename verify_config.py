#!/usr/bin/env python3
"""
验证配置文件中的向量维度设置
确保所有相关配置都设置为1024维度
"""

import toml
import os
import sys
from pathlib import Path

def find_config_files():
    """查找所有配置文件"""
    config_files = []
    
    # 查找r2r.toml文件
    possible_paths = [
        "backend/py/r2r/r2r.toml",
        "r2r.toml",
        "backend/py/r2r.toml",
        "config/r2r.toml"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            config_files.append(path)
    
    return config_files

def check_config_file(file_path):
    """检查单个配置文件的维度设置"""
    print(f"\n📄 检查配置文件: {file_path}")
    print("-" * 50)
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        issues = []
        fixes = []
        
        # 检查embedding配置
        if 'embedding' in config:
            embedding = config['embedding']
            base_dim = embedding.get('base_dimension')
            
            print(f"📊 embedding.base_dimension: {base_dim}")
            
            if base_dim != 1024:
                issues.append(f"embedding.base_dimension = {base_dim} (应该是1024)")
                fixes.append("embedding.base_dimension = 1024")
        else:
            issues.append("缺少 [embedding] 配置段")
        
        # 检查completion_embedding配置
        if 'completion_embedding' in config:
            comp_embedding = config['completion_embedding']
            comp_base_dim = comp_embedding.get('base_dimension')
            
            print(f"📊 completion_embedding.base_dimension: {comp_base_dim}")
            
            if comp_base_dim != 1024:
                issues.append(f"completion_embedding.base_dimension = {comp_base_dim} (应该是1024)")
                fixes.append("completion_embedding.base_dimension = 1024")
        else:
            issues.append("缺少 [completion_embedding] 配置段")
        
        # 检查其他相关配置
        if 'embedding' in config:
            embedding = config['embedding']
            
            # 检查模型配置
            base_model = embedding.get('base_model')
            print(f"🤖 embedding.base_model: {base_model}")
            
            # 检查量化设置
            if 'quantization_settings' in embedding:
                quant = embedding['quantization_settings']
                quant_type = quant.get('quantization_type', 'FP32')
                print(f"⚙️  quantization_type: {quant_type}")
        
        # 显示结果
        if issues:
            print(f"\n❌ 发现 {len(issues)} 个问题:")
            for issue in issues:
                print(f"   • {issue}")
            
            print(f"\n🔧 建议修复:")
            for fix in fixes:
                print(f"   • {fix}")
            
            return False, fixes
        else:
            print(f"\n✅ 配置正确!")
            return True, []
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False, []

def fix_config_file(file_path, fixes):
    """修复配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            config = toml.load(f)
        
        # 应用修复
        if 'embedding' in config:
            config['embedding']['base_dimension'] = 1024
        
        if 'completion_embedding' in config:
            config['completion_embedding']['base_dimension'] = 1024
        
        # 备份原文件
        backup_path = f"{file_path}.backup"
        if os.path.exists(file_path):
            import shutil
            shutil.copy2(file_path, backup_path)
            print(f"📋 已备份原文件到: {backup_path}")
        
        # 写入修复后的配置
        with open(file_path, 'w', encoding='utf-8') as f:
            toml.dump(config, f)
        
        print(f"✅ 配置文件已修复: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 修复配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 R2R配置文件维度检查工具")
    print("=" * 50)
    
    # 查找配置文件
    config_files = find_config_files()
    
    if not config_files:
        print("❌ 未找到配置文件!")
        print("请确保在正确的目录中运行此脚本")
        return
    
    print(f"📁 找到 {len(config_files)} 个配置文件:")
    for file in config_files:
        print(f"   • {file}")
    
    all_good = True
    all_fixes = []
    
    # 检查每个配置文件
    for config_file in config_files:
        is_good, fixes = check_config_file(config_file)
        if not is_good:
            all_good = False
            all_fixes.extend([(config_file, fixes)])
    
    # 总结
    print("\n" + "=" * 50)
    if all_good:
        print("✅ 所有配置文件都正确!")
    else:
        print("❌ 发现配置问题!")
        
        # 询问是否自动修复
        fix_confirm = input("\n🔧 是否自动修复配置文件? (输入 'YES' 继续): ")
        if fix_confirm == 'YES':
            print("\n🔄 开始修复配置文件...")
            for config_file, fixes in all_fixes:
                fix_config_file(config_file, fixes)
            
            print("\n✅ 配置文件修复完成!")
            print("📝 请重启R2R服务以应用新配置")
        else:
            print("❌ 跳过自动修复")
    
    print("\n📋 检查完成!")

if __name__ == "__main__":
    main()
