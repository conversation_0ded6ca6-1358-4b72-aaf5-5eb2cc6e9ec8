# AnythingChat 后端配置完整指南

## 概述

AnythingChat 基于 R2R 框架构建，支持多种配置方式。本文档详细说明了所有配置项的设置方法，包括外部 MinIO 存储、PostgreSQL 数据库和大模型 API 的配置。

## 配置文件结构

### 主要配置文件

1. **默认配置文件**: `backend/py/r2r/r2r.toml` - 系统默认配置
2. **环境变量文件**: `backend/.env.example` - 环境变量模板
3. **Docker环境文件**: `backend/r2r-full.env` - Docker部署环境变量
4. **预设配置文件**: `backend/py/core/configs/` - 各种预设配置

### 配置加载优先级

1. 环境变量 (最高优先级)
2. 指定的配置文件 (`R2R_CONFIG_PATH`)
3. 预设配置 (`R2R_CONFIG_NAME`)
4. 默认配置文件 (`r2r.toml`)

## 1. PostgreSQL 数据库配置

### 1.1 配置文件设置

在 `r2r.toml` 中的 `[database]` 部分：

```toml
[database]
provider = "postgres"
user = "your_username"           # 可选，优先使用环境变量
password = "your_password"       # 可选，优先使用环境变量
host = "localhost"               # 可选，优先使用环境变量
port = 5432                      # 可选，优先使用环境变量
db_name = "your_database"        # 可选，优先使用环境变量
project_name = "your_project"    # 可选，优先使用环境变量
default_collection_name = "Default"
default_collection_description = "Your default collection."

# PostgreSQL 性能调优设置
[database.postgres_configuration_settings]
max_connections = 256
statement_cache_size = 100
shared_buffers = 16384
work_mem = 4096
maintenance_work_mem = 65536
effective_cache_size = 524288
checkpoint_completion_target = 0.9
wal_buffers = 512
default_statistics_target = 100
random_page_cost = 4.0
effective_io_concurrency = 1
min_wal_size = 80
max_wal_size = 1024
max_worker_processes = 8
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2
huge_pages = "try"

# 数据库维护设置
[database.maintenance]
vacuum_schedule = "0 3 * * *"  # 每天凌晨3点执行清理

# 速率限制设置
[database.limits]
global_per_min = 60
route_per_min = 20
monthly_limit = 10000
```

### 1.2 环境变量设置

**必需的环境变量**：
```bash
# 基本连接参数
export R2R_POSTGRES_USER=postgres
export R2R_POSTGRES_PASSWORD=your_password
export R2R_POSTGRES_HOST=localhost
export R2R_POSTGRES_PORT=5432
export R2R_POSTGRES_DBNAME=r2r
export R2R_PROJECT_NAME=anythingchat

# 性能调优参数 (可选)
export R2R_POSTGRES_MAX_CONNECTIONS=1024
export R2R_POSTGRES_STATEMENT_CACHE_SIZE=100
export R2R_POSTGRES_SHARED_BUFFERS=16384
export R2R_POSTGRES_WORK_MEM=4096
export R2R_POSTGRES_MAINTENANCE_WORK_MEM=65536
export R2R_POSTGRES_EFFECTIVE_CACHE_SIZE=524288
export R2R_POSTGRES_CHECKPOINT_COMPLETION_TARGET=0.9
export R2R_POSTGRES_WAL_BUFFERS=512
export R2R_POSTGRES_DEFAULT_STATISTICS_TARGET=100
export R2R_POSTGRES_RANDOM_PAGE_COST=4.0
export R2R_POSTGRES_EFFECTIVE_IO_CONCURRENCY=1
export R2R_POSTGRES_MIN_WAL_SIZE=80
export R2R_POSTGRES_MAX_WAL_SIZE=1024
export R2R_POSTGRES_MAX_WORKER_PROCESSES=8
export R2R_POSTGRES_MAX_PARALLEL_WORKERS_PER_GATHER=2
export R2R_POSTGRES_MAX_PARALLEL_WORKERS=8
export R2R_POSTGRES_MAX_PARALLEL_MAINTENANCE_WORKERS=2
```

### 1.3 Docker 部署配置

使用 Docker 启动 PostgreSQL：
```bash
docker run -d \
  --name postgres-r2r \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=r2r \
  -p 5432:5432 \
  -v postgres_data:/var/lib/postgresql/data \
  ankane/pgvector
```

## 2. MinIO/S3 存储配置

### 2.1 配置文件设置

在 `r2r.toml` 中的 `[file]` 部分：

```toml
[file]
provider = "s3"                              # 使用 S3 兼容存储
bucket_name = "your-bucket-name"             # 存储桶名称
aws_access_key_id = "your-access-key"        # 访问密钥
aws_secret_access_key = "your-secret-key"    # 秘密密钥
region_name = "us-east-1"                    # 区域名称
endpoint_url = "http://localhost:9000"       # MinIO 服务器地址
```

### 2.2 环境变量设置

**MinIO/S3 环境变量**：
```bash
# S3/MinIO 基本配置
export S3_BUCKET_NAME=your-bucket-name
export AWS_ACCESS_KEY_ID=your-access-key
export AWS_SECRET_ACCESS_KEY=your-secret-key
export AWS_REGION=us-east-1
export S3_ENDPOINT_URL=http://localhost:9000  # MinIO 服务器地址

# 对于 MinIO，通常设置：
export S3_ENDPOINT_URL=http://your-minio-server:9000
export AWS_REGION=us-east-1  # MinIO 可以使用任意区域名
```

### 2.3 MinIO 服务器部署

使用 Docker 启动 MinIO：
```bash
docker run -d \
  --name minio \
  -p 9000:9000 \
  -p 9001:9001 \
  -e MINIO_ROOT_USER=minioadmin \
  -e MINIO_ROOT_PASSWORD=minioadmin \
  -v minio_data:/data \
  minio/minio server /data --console-address ":9001"
```

访问 MinIO 控制台：`http://localhost:9001`

## 3. 大模型 API 配置

### 3.1 LLM 模型配置

在 `r2r.toml` 中的 `[app]` 部分：

```toml
[app]
# 内部操作使用的快速模型
fast_llm = "openai/gpt-4.1-mini"
# 用户交互使用的高质量模型  
quality_llm = "openai/gpt-4.1"
# 视觉输入处理模型
vlm = "openai/gpt-4.1"
# 音频转录模型
audio_lm = "openai/whisper-1"
# 推理模型（研究代理使用）
reasoning_llm = "openai/o3-mini"
# 规划模型（研究代理使用）
planning_llm = "anthropic/claude-3-7-sonnet-20250219"
```

### 3.2 完成配置

```toml
[completion]
provider = "r2r"                    # 或 "litellm"
concurrent_request_limit = 64       # 并发请求限制
request_timeout = 60                # 请求超时时间（秒）
max_retries = 3                     # 最大重试次数
initial_backoff = 1.0               # 初始退避时间
max_backoff = 64.0                  # 最大退避时间

[completion.generation_config]
temperature = 0.1                   # 生成温度
top_p = 1                          # Top-p 采样
max_tokens_to_sample = 4_096       # 最大生成token数
stream = false                     # 是否流式输出
add_generation_kwargs = {}         # 额外生成参数
```

### 3.3 嵌入模型配置

```toml
[embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-small"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256
initial_backoff = 1.0
max_retries = 3
max_backoff = 64.0

# 向量量化设置
[embedding.quantization_settings]
quantization_type = "FP32"

# 完成嵌入配置（通常与嵌入配置相同）
[completion_embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-small"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256
```

### 3.4 环境变量设置

**OpenAI 配置**：
```bash
export OPENAI_API_KEY=sk-your-openai-api-key
export OPENAI_API_BASE=https://api.openai.com/v1  # 可选，自定义API基础URL
```

**Anthropic 配置**：
```bash
export ANTHROPIC_API_KEY=your-anthropic-api-key
```

**Azure OpenAI 配置**：
```bash
export AZURE_API_KEY=your-azure-api-key
export AZURE_API_BASE=https://your-resource.openai.azure.com
export AZURE_API_VERSION=2024-02-15-preview
```

**其他提供商**：
```bash
# Google Gemini
export GEMINI_API_KEY=your-gemini-api-key

# Mistral
export MISTRAL_API_KEY=your-mistral-api-key

# Groq
export GROQ_API_KEY=your-groq-api-key

# Cohere
export COHERE_API_KEY=your-cohere-api-key

# XAI/Grok
export XAI_API_KEY=your-xai-api-key

# Ollama (本地部署)
export OLLAMA_API_BASE=http://localhost:11434

# LM Studio (本地部署)
export LM_STUDIO_API_BASE=http://localhost:1234
export LM_STUDIO_API_KEY=1234
```

## 4. 配置文件示例

### 4.1 生产环境配置示例

创建 `production.toml`：
```toml
[app]
project_name = "anythingchat_prod"
fast_llm = "openai/gpt-4.1-mini"
quality_llm = "openai/gpt-4.1"
vlm = "openai/gpt-4.1"
audio_lm = "openai/whisper-1"

[database]
provider = "postgres"
default_collection_name = "Production"

[database.postgres_configuration_settings]
max_connections = 512
statement_cache_size = 200

[file]
provider = "s3"

[completion]
provider = "r2r"
concurrent_request_limit = 128
request_timeout = 30

[embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-large"
base_dimension = 3072
concurrent_request_limit = 512

[auth]
provider = "r2r"
require_authentication = true
require_email_verification = true
access_token_lifetime_in_minutes = 60
refresh_token_lifetime_in_days = 7
```

### 4.2 开发环境配置示例

创建 `development.toml`：
```toml
[app]
project_name = "anythingchat_dev"
fast_llm = "openai/gpt-4.1-mini"
quality_llm = "openai/gpt-4.1-mini"  # 开发环境使用便宜的模型

[database]
provider = "postgres"

[file]
provider = "postgres"  # 开发环境使用数据库存储文件

[completion]
provider = "r2r"
concurrent_request_limit = 32

[embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-small"
base_dimension = 512

[auth]
provider = "r2r"
require_authentication = false  # 开发环境不需要认证
```

## 5. 配置操作步骤

### 5.1 使用环境变量配置

1. **创建环境变量文件**：
```bash
cp backend/.env.example backend/.env
```

2. **编辑环境变量**：
```bash
# 编辑 .env 文件
nano backend/.env
```

3. **加载环境变量**：
```bash
source backend/.env
```

### 5.2 使用配置文件

1. **选择预设配置**：
```bash
export R2R_CONFIG_NAME=full  # 使用 full.toml 配置
```

2. **使用自定义配置文件**：
```bash
export R2R_CONFIG_PATH=/path/to/your/config.toml
```

3. **验证配置**：
```bash
python -c "from r2r import R2RConfig; config = R2RConfig.load(); print('配置加载成功')"
```

### 5.3 Docker 部署配置

1. **使用环境文件**：
```bash
docker run -d \
  --name r2r-backend \
  --env-file backend/r2r-full.env \
  -p 7272:7272 \
  your-r2r-image
```

2. **使用 docker-compose**：
```yaml
version: '3.8'
services:
  r2r:
    image: your-r2r-image
    env_file:
      - backend/r2r-full.env
    ports:
      - "7272:7272"
    depends_on:
      - postgres
      - minio
```

## 6. 注意事项和最佳实践

### 6.1 安全注意事项

1. **敏感信息保护**：
   - 不要在配置文件中硬编码API密钥
   - 使用环境变量存储敏感信息
   - 在生产环境中使用密钥管理服务

2. **数据库安全**：
   - 使用强密码
   - 限制数据库访问权限
   - 启用SSL连接

3. **文件存储安全**：
   - 配置适当的存储桶权限
   - 使用IAM角色而非长期密钥（AWS）
   - 启用存储加密

### 6.2 性能优化

1. **数据库优化**：
   - 根据硬件配置调整连接池大小
   - 监控查询性能
   - 定期执行数据库维护

2. **API调用优化**：
   - 合理设置并发限制
   - 配置重试和退避策略
   - 监控API使用量和成本

3. **存储优化**：
   - 选择合适的存储类型
   - 配置生命周期策略
   - 监控存储使用量

### 6.3 监控和日志

1. **启用详细日志**：
```bash
export R2R_LOG_LEVEL=DEBUG
```

2. **配置错误监控**：
```bash
export R2R_SENTRY_DSN=your-sentry-dsn
```

3. **健康检查**：
```bash
curl http://localhost:7272/health
```

## 7. 故障排除

### 7.1 常见问题

1. **数据库连接失败**：
   - 检查环境变量设置
   - 验证数据库服务状态
   - 检查网络连接

2. **API调用失败**：
   - 验证API密钥
   - 检查API配额和限制
   - 确认模型名称正确

3. **文件存储问题**：
   - 验证存储桶权限
   - 检查网络连接
   - 确认凭据正确

### 7.2 调试命令

```bash
# 检查配置加载
python -c "from r2r import R2RConfig; print(R2RConfig.load().to_toml())"

# 测试数据库连接
python -c "import asyncpg; import asyncio; asyncio.run(asyncpg.connect('postgresql://user:pass@host:port/db'))"

# 测试API调用
python -c "import openai; print(openai.models.list())"
```

## 8. 完整配置文件清单

### 8.1 核心配置文件

| 文件路径 | 用途 | 说明 |
|---------|------|------|
| `backend/py/r2r/r2r.toml` | 默认配置 | 系统默认配置文件 |
| `backend/py/core/configs/full.toml` | 完整功能配置 | 包含所有功能的配置 |
| `backend/py/core/configs/full_azure.toml` | Azure配置 | 使用Azure服务的配置 |
| `backend/py/core/configs/full_ollama.toml` | Ollama配置 | 使用本地Ollama的配置 |
| `backend/py/core/configs/r2r_with_auth.toml` | 认证配置 | 启用认证功能的配置 |
| `backend/.env.example` | 环境变量模板 | 环境变量配置示例 |
| `backend/r2r-full.env` | Docker环境变量 | Docker部署环境变量 |

### 8.2 配置项完整列表

#### 应用配置 [app]
```toml
[app]
project_name = "your_project"                    # 项目名称
default_max_documents_per_user = 10_000          # 每用户最大文档数
default_max_chunks_per_user = 10_000_000         # 每用户最大块数
default_max_collections_per_user = 5_000         # 每用户最大集合数
default_max_upload_size = 214748364800           # 最大上传大小(字节)
fast_llm = "openai/gpt-4.1-mini"                # 快速LLM模型
quality_llm = "openai/gpt-4.1"                  # 高质量LLM模型
vlm = "openai/gpt-4.1"                          # 视觉语言模型
audio_lm = "openai/whisper-1"                   # 音频转录模型
reasoning_llm = "openai/o3-mini"                # 推理模型
planning_llm = "anthropic/claude-3-7-sonnet"   # 规划模型
```

#### 认证配置 [auth]
```toml
[auth]
provider = "r2r"                                # 认证提供商
secret_key = "your-secret-key"                  # JWT密钥
access_token_lifetime_in_minutes = 60000        # 访问令牌生命周期
refresh_token_lifetime_in_days = 7              # 刷新令牌生命周期
require_authentication = false                  # 是否需要认证
require_email_verification = false              # 是否需要邮箱验证
default_admin_email = "<EMAIL>"       # 默认管理员邮箱
default_admin_password = "change_me_immediately" # 默认管理员密码
```

#### 数据摄取配置 [ingestion]
```toml
[ingestion]
provider = "r2r"                               # 摄取提供商
chunking_strategy = "recursive"                # 分块策略
chunk_size = 1_024                            # 块大小
chunk_overlap = 512                           # 块重叠
excluded_parsers = []                         # 排除的解析器
automatic_extraction = true                   # 自动实体关系提取
vlm_batch_size = 20                          # 视觉模型批处理大小
max_concurrent_vlm_tasks = 20                # 最大并发视觉任务
vlm_ocr_one_page_per_chunk = true           # 每块一页OCR

[ingestion.chunk_enrichment_settings]
chunk_enrichment_prompt = "chunk_enrichment"  # 块增强提示
enable_chunk_enrichment = false              # 启用块增强
n_chunks = 2                                 # 增强时使用的块数

[ingestion.extra_parsers]
pdf = ["zerox", "ocr"]                       # PDF额外解析器
```

#### 编排配置 [orchestration]
```toml
[orchestration]
provider = "simple"                          # 编排提供商
max_runs = 2048                             # 最大运行数
kg_creation_concurrency_limit = 32          # 知识图谱创建并发限制
ingestion_concurrency_limit = 16            # 摄取并发限制
kg_concurrency_limit = 4                    # 知识图谱并发限制
```

#### OCR配置 [ocr]
```toml
[ocr]
provider = "mistral"                         # OCR提供商
model = "mistral-ocr-latest"                # OCR模型
```

#### 邮件配置 [email]
```toml
[email]
provider = "console_mock"                    # 邮件提供商
smtp_server = "smtp.gmail.com"              # SMTP服务器
smtp_port = 587                             # SMTP端口
smtp_username = "<EMAIL>"      # SMTP用户名
smtp_password = "your-password"             # SMTP密码
from_email = "<EMAIL>"          # 发件人邮箱
use_tls = true                              # 使用TLS
sendgrid_api_key = "your-sendgrid-key"      # SendGrid API密钥
mailersend_api_key = "your-mailersend-key"  # MailerSend API密钥
```

#### 调度器配置 [scheduler]
```toml
[scheduler]
provider = "apscheduler"                     # 调度器提供商
```

#### 加密配置 [crypto]
```toml
[crypto]
provider = "bcrypt"                          # 加密提供商
```

## 9. 环境变量完整列表

### 9.1 系统环境变量
```bash
# R2R 核心配置
R2R_CONFIG_NAME=full                         # 配置名称
R2R_CONFIG_PATH=/path/to/config.toml        # 配置文件路径
R2R_PROJECT_NAME=anythingchat               # 项目名称
R2R_HOST=0.0.0.0                           # 服务主机
R2R_PORT=7272                              # 服务端口
R2R_LOG_LEVEL=INFO                         # 日志级别
R2R_SECRET_KEY=your-secret-key             # 系统密钥
```

### 9.2 数据库环境变量
```bash
# PostgreSQL 基本配置
R2R_POSTGRES_USER=postgres                  # 数据库用户
R2R_POSTGRES_PASSWORD=password              # 数据库密码
R2R_POSTGRES_HOST=localhost                 # 数据库主机
R2R_POSTGRES_PORT=5432                     # 数据库端口
R2R_POSTGRES_DBNAME=r2r                   # 数据库名称

# PostgreSQL 性能配置
R2R_POSTGRES_MAX_CONNECTIONS=1024          # 最大连接数
R2R_POSTGRES_STATEMENT_CACHE_SIZE=100      # 语句缓存大小
R2R_POSTGRES_SHARED_BUFFERS=16384          # 共享缓冲区
R2R_POSTGRES_WORK_MEM=4096                 # 工作内存
R2R_POSTGRES_MAINTENANCE_WORK_MEM=65536    # 维护工作内存
R2R_POSTGRES_EFFECTIVE_CACHE_SIZE=524288   # 有效缓存大小
```

### 9.3 存储环境变量
```bash
# S3/MinIO 配置
S3_BUCKET_NAME=your-bucket                  # 存储桶名称
AWS_ACCESS_KEY_ID=your-access-key          # 访问密钥ID
AWS_SECRET_ACCESS_KEY=your-secret-key      # 秘密访问密钥
AWS_REGION=us-east-1                       # AWS区域
S3_ENDPOINT_URL=http://localhost:9000      # S3端点URL(MinIO)
```

### 9.4 LLM API环境变量
```bash
# OpenAI
OPENAI_API_KEY=sk-your-key                 # OpenAI API密钥
OPENAI_API_BASE=https://api.openai.com/v1  # OpenAI API基础URL

# Anthropic
ANTHROPIC_API_KEY=your-key                 # Anthropic API密钥

# Azure OpenAI
AZURE_API_KEY=your-key                     # Azure API密钥
AZURE_API_BASE=https://your.openai.azure.com # Azure API基础URL
AZURE_API_VERSION=2024-02-15-preview       # Azure API版本

# Google
GEMINI_API_KEY=your-key                    # Gemini API密钥
GOOGLE_APPLICATION_CREDENTIALS=/path/to/creds.json # Google凭据文件
VERTEX_PROJECT=your-project                # Vertex AI项目
VERTEX_LOCATION=us-central1                # Vertex AI位置

# 其他提供商
MISTRAL_API_KEY=your-key                   # Mistral API密钥
GROQ_API_KEY=your-key                      # Groq API密钥
COHERE_API_KEY=your-key                    # Cohere API密钥
XAI_API_KEY=your-key                       # XAI API密钥

# 本地部署
OLLAMA_API_BASE=http://localhost:11434     # Ollama API基础URL
LM_STUDIO_API_BASE=http://localhost:1234   # LM Studio API基础URL
LM_STUDIO_API_KEY=1234                     # LM Studio API密钥
```

### 9.5 其他服务环境变量
```bash
# 文档处理
UNSTRUCTURED_API_KEY=your-key              # Unstructured API密钥
UNSTRUCTURED_API_URL=https://api.unstructured.io/general/v0/general

# 网络搜索
TAVILY_API_KEY=your-key                    # Tavily API密钥
SERPER_API_KEY=your-key                    # Serper API密钥
FIRECRAWL_API_KEY=your-key                 # Firecrawl API密钥

# 邮件服务
SENDGRID_API_KEY=your-key                  # SendGrid API密钥
MAILERSEND_API_KEY=your-key                # MailerSend API密钥

# OAuth
GOOGLE_CLIENT_ID=your-client-id            # Google OAuth客户端ID
GOOGLE_CLIENT_SECRET=your-client-secret    # Google OAuth客户端密钥
GITHUB_CLIENT_ID=your-client-id            # GitHub OAuth客户端ID
GITHUB_CLIENT_SECRET=your-client-secret    # GitHub OAuth客户端密钥

# 监控
R2R_SENTRY_DSN=your-sentry-dsn            # Sentry DSN
R2R_SENTRY_ENVIRONMENT=production          # Sentry环境
```

## 10. 部署配置示例

### 10.1 Docker Compose 完整配置

创建 `docker-compose.yml`：
```yaml
version: '3.8'

services:
  postgres:
    image: ankane/pgvector:latest
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: r2r
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  minio:
    image: minio/minio:latest
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 5

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  r2r:
    image: your-r2r-image:latest
    environment:
      # 系统配置
      R2R_CONFIG_NAME: full
      R2R_PROJECT_NAME: anythingchat
      R2R_HOST: 0.0.0.0
      R2R_PORT: 7272

      # 数据库配置
      R2R_POSTGRES_USER: postgres
      R2R_POSTGRES_PASSWORD: postgres
      R2R_POSTGRES_HOST: postgres
      R2R_POSTGRES_PORT: 5432
      R2R_POSTGRES_DBNAME: r2r

      # 存储配置
      S3_BUCKET_NAME: anythingchat
      AWS_ACCESS_KEY_ID: minioadmin
      AWS_SECRET_ACCESS_KEY: minioadmin
      AWS_REGION: us-east-1
      S3_ENDPOINT_URL: http://minio:9000

      # LLM配置
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
    ports:
      - "7272:7272"
    depends_on:
      postgres:
        condition: service_healthy
      minio:
        condition: service_healthy
    volumes:
      - ./configs:/app/configs
      - ./logs:/app/logs

volumes:
  postgres_data:
  minio_data:
  redis_data:
```

### 10.2 Kubernetes 配置示例

创建 `k8s-deployment.yaml`：
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: r2r-config
data:
  R2R_CONFIG_NAME: "full"
  R2R_PROJECT_NAME: "anythingchat"
  R2R_HOST: "0.0.0.0"
  R2R_PORT: "7272"
  R2R_POSTGRES_HOST: "postgres-service"
  R2R_POSTGRES_PORT: "5432"
  R2R_POSTGRES_DBNAME: "r2r"
  S3_ENDPOINT_URL: "http://minio-service:9000"
  AWS_REGION: "us-east-1"

---
apiVersion: v1
kind: Secret
metadata:
  name: r2r-secrets
type: Opaque
stringData:
  R2R_POSTGRES_USER: "postgres"
  R2R_POSTGRES_PASSWORD: "postgres"
  AWS_ACCESS_KEY_ID: "minioadmin"
  AWS_SECRET_ACCESS_KEY: "minioadmin"
  OPENAI_API_KEY: "your-openai-key"
  ANTHROPIC_API_KEY: "your-anthropic-key"

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: r2r-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: r2r
  template:
    metadata:
      labels:
        app: r2r
    spec:
      containers:
      - name: r2r
        image: your-r2r-image:latest
        ports:
        - containerPort: 7272
        envFrom:
        - configMapRef:
            name: r2r-config
        - secretRef:
            name: r2r-secrets
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 7272
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 7272
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: r2r-service
spec:
  selector:
    app: r2r
  ports:
  - protocol: TCP
    port: 80
    targetPort: 7272
  type: LoadBalancer
```

---

本配置指南涵盖了 AnythingChat 后端的所有主要配置选项。根据您的具体需求选择合适的配置方式，并遵循最佳实践确保系统的安全性和性能。如需更多帮助，请参考项目文档或联系技术支持。
