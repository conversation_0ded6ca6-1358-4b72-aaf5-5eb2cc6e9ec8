# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
.env*
.DS_Store

#vscode
.vscode/settings.json

#logs
logs.json

# dependencies
/node_modules
/cloud-docs/node_modules
/.pnp
.pnp.js
.next

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# typescript
*.tsbuildinfo
.vercel

# Sentry Config File
.env.sentry-build-plugin
