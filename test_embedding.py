#!/usr/bin/env python3
"""
测试embedding配置的脚本
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加backend路径到sys.path
backend_path = Path(__file__).parent / "backend" / "py"
sys.path.insert(0, str(backend_path))

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["OPENAI_BASE_URL"] = "http://172.17.9.46:3000/v1"
os.environ["ALIYUN_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["ALIYUN_API_BASE"] = "http://172.17.9.46:3000/v1"

try:
    # 首先测试litellm是否可用
    import litellm
    print("✅ 成功导入litellm")

    # 测试直接调用litellm
    print("🔄 测试直接调用litellm...")

    # 设置litellm的API配置
    litellm.api_key = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
    litellm.api_base = "http://172.17.9.46:3000/v1"

    # 测试embedding调用
    response = litellm.embedding(
        model="openai/aliyun/text-embedding-v4",
        input=["测试文本"],
        api_key="sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI",
        api_base="http://172.17.9.46:3000/v1"
    )

    print(f"✅ LiteLLM直接调用成功!")
    print(f"   - 响应类型: {type(response)}")
    if hasattr(response, 'data') and response.data:
        print(f"   - 向量维度: {len(response.data[0]['embedding'])}")
        print(f"   - 前5个值: {response.data[0]['embedding'][:5]}")

    print("✅ 成功导入所需模块")
    
    # 测试配置
    config_data = {
        "provider": "litellm",
        "base_model": "openai/aliyun/text-embedding-v4",
        "base_dimension": 512,
        "batch_size": 128,
        "concurrent_request_limit": 256,
        "api_base": "http://172.17.9.46:3000/v1"
    }
    
    print(f"📋 配置数据: {config_data}")
    
    # 创建配置对象
    config = EmbeddingConfig.create(**config_data)
    print(f"✅ 成功创建EmbeddingConfig")
    print(f"   - provider: {config.provider}")
    print(f"   - base_model: {config.base_model}")
    print(f"   - base_dimension: {config.base_dimension}")
    print(f"   - extra_fields: {config.extra_fields}")
    
    # 创建provider
    provider = LiteLLMEmbeddingProvider(config)
    print(f"✅ 成功创建LiteLLMEmbeddingProvider")
    
    # 测试_get_embedding_kwargs方法
    kwargs = provider._get_embedding_kwargs()
    print(f"📋 Embedding kwargs: {kwargs}")
    
    # 测试实际的embedding调用
    async def test_embedding():
        try:
            print("🔄 开始测试embedding调用...")
            result = await provider.async_get_embedding("测试文本")
            print(f"✅ Embedding调用成功!")
            print(f"   - 向量维度: {len(result)}")
            print(f"   - 前5个值: {result[:5]}")
            return True
        except Exception as e:
            print(f"❌ Embedding调用失败: {str(e)}")
            print(f"   - 错误类型: {type(e).__name__}")
            return False
    
    # 运行异步测试
    success = asyncio.run(test_embedding())
    
    if success:
        print("\n🎉 所有测试通过！embedding配置正确。")
    else:
        print("\n⚠️  embedding调用失败，需要进一步调试。")
        
except ImportError as e:
    print(f"❌ 导入模块失败: {str(e)}")
    print("请确保在正确的Python环境中运行此脚本")
except Exception as e:
    print(f"❌ 测试过程中出现错误: {str(e)}")
    print(f"   - 错误类型: {type(e).__name__}")
