#!/usr/bin/env python3
"""
测试NewAPI provider的脚本
"""
import os
import sys
import asyncio
from pathlib import Path

# 添加backend路径到sys.path
backend_path = Path(__file__).parent / "backend" / "py"
sys.path.insert(0, str(backend_path))

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["OPENAI_BASE_URL"] = "http://172.17.9.46:3000/v1"
os.environ["ALIYUN_API_KEY"] = "sk-SYHJNVCHCmSye2mfAOrUjR3OsKVGEitMypmJKQtAWJrwIoYI"
os.environ["ALIYUN_API_BASE"] = "http://172.17.9.46:3000/v1"

print("🔧 测试NewAPI embedding provider...")
print(f"📍 Backend路径: {backend_path}")
print(f"🔑 API密钥: {os.environ['OPENAI_API_KEY'][:20]}...")
print(f"🌐 API地址: http://172.17.9.46:3000/v1")

try:
    # 导入必要的模块
    from core.base.providers.embedding import EmbeddingConfig
    from core.providers.embeddings.newapi import NewAPIEmbeddingProvider
    
    print("✅ 成功导入NewAPI provider模块")
    
    # 测试配置创建
    config_data = {
        "provider": "newapi",
        "base_model": "aliyun/text-embedding-v4",
        "base_dimension": 1024,
        "batch_size": 128,
        "concurrent_request_limit": 256,
        "api_base": "http://172.17.9.46:3000/v1"
    }
    
    print(f"📋 创建配置: {config_data}")
    
    # 创建配置对象
    config = EmbeddingConfig.create(**config_data)
    print(f"✅ 成功创建EmbeddingConfig")
    print(f"   - provider: {config.provider}")
    print(f"   - base_model: {config.base_model}")
    print(f"   - base_dimension: {config.base_dimension}")
    print(f"   - extra_fields: {getattr(config, 'extra_fields', {})}")
    
    # 创建provider
    provider = NewAPIEmbeddingProvider(config)
    print(f"✅ 成功创建NewAPIEmbeddingProvider")
    
    # 测试_get_embedding_kwargs方法
    kwargs = provider._get_embedding_kwargs()
    print(f"📋 生成的embedding kwargs:")
    for key, value in kwargs.items():
        if key == 'api_key' and value:
            print(f"   - {key}: {value[:20]}...")
        else:
            print(f"   - {key}: {value}")
    
    # 检查关键参数
    checks = []
    if 'api_base' in kwargs:
        print("✅ api_base 正确传递")
        checks.append(True)
    else:
        print("❌ api_base 未传递")
        checks.append(False)
        
    if 'api_key' in kwargs:
        print("✅ api_key 正确传递")
        checks.append(True)
    else:
        print("❌ api_key 未传递")
        checks.append(False)
    
    if kwargs.get('model') == 'aliyun/text-embedding-v4':
        print("✅ 模型名称正确")
        checks.append(True)
    else:
        print("❌ 模型名称不正确")
        checks.append(False)
        
    if 'dimensions' not in kwargs:
        print("✅ dimensions参数已正确排除")
        checks.append(True)
    else:
        print("❌ dimensions参数仍然存在")
        checks.append(False)
    
    if all(checks):
        print("\n🎉 所有配置检查通过！")
        print("💡 NewAPI provider配置正确，应该能够正常工作")
    else:
        print("\n⚠️  部分配置检查失败")
    
    print("\n💡 建议：")
    print("   1. 确保new-api服务在 http://172.17.9.46:3000 正常运行")
    print("   2. 确认模型 'aliyun/text-embedding-v4' 在new-api中正确配置")
    print("   3. 重启R2R服务以应用NewAPI provider")
    
except ImportError as e:
    print(f"❌ 导入模块失败: {str(e)}")
    print("💡 请确保在正确的Python环境中运行此脚本")
    print("   建议使用: uv run python test_newapi_provider.py")
except Exception as e:
    print(f"❌ 测试过程中出现错误: {str(e)}")
    print(f"   - 错误类型: {type(e).__name__}")
    import traceback
    traceback.print_exc()
