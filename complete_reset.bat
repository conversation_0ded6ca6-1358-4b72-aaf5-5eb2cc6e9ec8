@echo off
chcp 65001 >nul
echo ==========================================
echo R2R系统完整重置工具 - 统一1024维度
echo ==========================================
echo.

echo 🎯 此工具将执行以下操作:
echo 1. 检查并修复配置文件维度设置
echo 2. 完全重置数据库表结构
echo 3. 重启R2R服务
echo.

echo ⚠️  警告: 这将删除所有现有数据!
echo.
set /p confirm="确认继续完整重置? (输入 YES 继续): "

if not "%confirm%"=="YES" (
    echo ❌ 操作已取消
    pause
    exit /b 0
)

echo.
echo 🚀 开始完整重置流程...
echo.

:: 步骤1: 检查Python环境
echo 📋 步骤1: 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或不在PATH中
    echo 请安装Python 3.7+
    pause
    exit /b 1
)
echo ✅ Python环境正常

:: 安装必要的Python包
echo 📦 安装必要的依赖...
pip install toml asyncpg >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  依赖安装可能有问题，继续执行...
)

:: 步骤2: 检查和修复配置文件
echo.
echo 📋 步骤2: 检查配置文件...
python verify_config.py
if %errorlevel% neq 0 (
    echo ❌ 配置文件检查失败
    pause
    exit /b 1
)

:: 步骤3: 停止R2R服务
echo.
echo 📋 步骤3: 停止R2R服务...
echo 🔍 查找R2R进程...

:: 尝试优雅停止
tasklist /FI "IMAGENAME eq python.exe" /FO CSV | findstr "r2r\|serve" >nul 2>&1
if %errorlevel% equ 0 (
    echo 🛑 发现R2R进程，尝试停止...
    taskkill /F /IM python.exe /FI "WINDOWTITLE eq *r2r*" >nul 2>&1
    taskkill /F /IM python.exe /FI "WINDOWTITLE eq *serve*" >nul 2>&1
    timeout /t 3 >nul
)

:: 检查端口占用
netstat -ano | findstr ":7272" >nul 2>&1
if %errorlevel% equ 0 (
    echo 🔍 端口7272仍被占用，强制释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":7272"') do (
        taskkill /F /PID %%a >nul 2>&1
    )
)

echo ✅ R2R服务已停止

:: 步骤4: 重置数据库
echo.
echo 📋 步骤4: 重置数据库...
call reset_database.bat
if %errorlevel% neq 0 (
    echo ❌ 数据库重置失败
    pause
    exit /b 1
)

:: 步骤5: 重启R2R服务
echo.
echo 📋 步骤5: 重启R2R服务...
echo 🚀 启动R2R服务...

:: 切换到正确的目录
if exist "backend\py" (
    cd backend\py
) else if exist "py" (
    cd py
)

:: 检查虚拟环境
if exist ".venv\Scripts\activate.bat" (
    echo 🐍 激活虚拟环境...
    call .venv\Scripts\activate.bat
)

:: 启动服务
echo 🌐 启动R2R服务器...
start "R2R Server" cmd /k "python -m r2r serve --host 0.0.0.0 --port 7272"

:: 等待服务启动
echo ⏳ 等待服务启动...
timeout /t 10 >nul

:: 检查服务状态
echo 🔍 检查服务状态...
curl -s http://localhost:7272/v3/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ R2R服务启动成功!
    echo 🌐 服务地址: http://localhost:7272
) else (
    echo ⚠️  服务可能还在启动中...
    echo 🌐 请稍后访问: http://localhost:7272
)

echo.
echo ==========================================
echo ✅ 完整重置流程完成!
echo ==========================================
echo.
echo 📝 重置摘要:
echo • 配置文件已统一为1024维度
echo • 数据库表已完全重建
echo • R2R服务已重启
echo.
echo 🎯 接下来可以:
echo 1. 访问 http://localhost:7272 验证服务
echo 2. 重新导入数据
echo 3. 测试图RAG功能
echo.

pause
