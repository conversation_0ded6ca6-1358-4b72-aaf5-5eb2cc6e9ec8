#!/usr/bin/env python3
"""
简化的R2R配置测试脚本
"""

import os
import requests
import time
import json

# 设置环境变量
os.environ["OPENAI_API_KEY"] = "sk-onCliIrxDPaFG2tXZ3zhpXrPu5MrqZeWujnswtjeN7AsiKz8"
os.environ["OPENAI_BASE_URL"] = "http://***********:3000/v1"

def test_r2r_api():
    """测试R2R API是否正常工作"""
    print("=== 测试R2R API ===")
    
    # R2R API的默认地址
    r2r_base_url = "http://localhost:7272"
    
    try:
        # 测试健康检查端点
        health_url = f"{r2r_base_url}/v3/health"
        print(f"检查健康状态: {health_url}")
        
        response = requests.get(health_url, timeout=10)
        if response.status_code == 200:
            print("✅ R2R服务运行正常")
            print(f"响应: {response.json()}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到R2R服务，请确保服务已启动")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_completion_api():
    """测试R2R的completion API"""
    print("\n=== 测试R2R Completion API ===")
    
    r2r_base_url = "http://localhost:7272"
    
    try:
        # 测试completion端点
        completion_url = f"{r2r_base_url}/v3/completion"
        
        payload = {
            "messages": [
                {"role": "user", "content": "你好，请简单介绍一下你自己。"}
            ],
            "generation_config": {
                "model": "openai/volcengine/deepseek-v3",
                "temperature": 0.1,
                "max_tokens_to_sample": 100
            }
        }
        
        print(f"发送请求到: {completion_url}")
        print(f"请求数据: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        response = requests.post(
            completion_url, 
            json=payload, 
            timeout=30,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Completion API测试成功")
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"❌ Completion API测试失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Completion API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试R2R配置...")
    print(f"API Key: {os.environ.get('OPENAI_API_KEY', 'Not Set')}")
    print(f"Base URL: {os.environ.get('OPENAI_BASE_URL', 'Not Set')}")
    
    results = []
    
    # 测试R2R服务健康状态
    results.append(test_r2r_api())
    
    # 如果服务正常，测试completion API
    if results[0]:
        results.append(test_completion_api())
    else:
        print("\n⚠️  R2R服务未运行，跳过API测试")
        print("请先启动R2R服务：")
        print("  cd backend")
        print("  python -m r2r serve --config-name=r2r")
        results.append(False)
    
    # 总结结果
    print("\n=== 测试结果总结 ===")
    test_names = ["R2R服务健康检查", "R2R Completion API"]
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    all_passed = all(results)
    print(f"\n总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if not all_passed:
        print("\n📝 配置说明:")
        print("1. 环境变量已正确设置")
        print("2. OpenAI客户端可以正常连接外部API")
        print("3. 如需测试完整的R2R功能，请启动R2R服务")
    
    return all_passed

if __name__ == "__main__":
    main()
